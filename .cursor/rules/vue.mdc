---
description: 
globs: 
alwaysApply: true
---

# TypeScript (Vue.js)

# TypeScript with Vue.js: Best Practices and Guidelines

## Code Style and Structure
- **Component Structure**: Organize components into single-file components (SFCs) with `.vue` extension.
- **File Naming**: Use PascalCase for component files (e.g., `MyComponent.vue`).
- **Directory Structure**: Group related components into directories (e.g., `components/`, `views/`).

## Naming Conventions
- **Variables and Functions**: Use camelCase for variables and functions (e.g., `myVariable`, `myFunction`).
- **Component Names**: Use PascalCase for component names (e.g., `MyComponent`).
- **Constants**: Use UPPER_SNAKE_CASE for constants (e.g., `MAX_ITEMS`).

## TypeScript Usage
- **Type Annotations**: Always annotate function parameters and return types.
- **Interfaces and Types**: Prefer interfaces for defining object shapes and types for unions and intersections.
- **Generics**: Use generics for reusable and type-safe components and functions.

## Syntax and Formatting
- **Indentation**: Use 2 spaces for indentation.
- **Semicolons**: Avoid semicolons unless necessary.
- **Line Length**: Keep lines under 80 characters for readability.

## Error Handling and Validation
- **Try/Catch**: Use `try/catch` blocks for handling asynchronous errors.
- **Validation**: Validate props using Vue's `PropType` and TypeScript types.
- **Error Boundaries**: Implement global error boundaries to catch runtime errors.

## UI and Styling
- **CSS-in-JS**: Use scoped styles within Vue components.
- **Tailwind CSS**: Integrate Tailwind CSS for utility-first styling.
- **Responsive Design**: Ensure components are responsive using Tailwind's responsive utilities.

## Performance Optimization
- **Lazy Loading**: Use Vue's `defineAsyncComponent` for lazy loading components.
- **Memoization**: Use `computed` properties and `watch` for efficient state management.
- **Tree Shaking**: Enable tree shaking to remove unused code during build.

## Key Conventions
- **Follow Vue.js Docs**: Adhere to Vue.js documentation for best practices and conventions.
- **Consistency**: Maintain consistent coding style across the project.
- **Code Reviews**: Regularly review code to ensure adherence to guidelines.
- **Project main language** This project main language is English.

