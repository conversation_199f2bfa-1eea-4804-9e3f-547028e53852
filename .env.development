# Whether to open mock
VITE_USE_MOCK = false

# public path
VITE_PUBLIC_PATH = /

# Cross-domain proxy, you can configure multiple
# Please note that no line breaks
# VITE_PROXY = [["/basic-api","https://meetok-backend.atreez.com"],["/upload","http://localhost:3300/upload"]]
# VITE_PROXY=[["/api","https://vvbin.cn/test"]]

# Delete console
VITE_DROP_CONSOLE = false

# Basic interface address SPA
VITE_GLOB_API_URL=http://localhost:5337
# VITE_GLOB_API_URL=https://starcheck-backend.atreez.com

# File upload address， optional
VITE_GLOB_UPLOAD_URL=/upload

# Interface prefix
VITE_GLOB_API_URL_PREFIX=


VITE_BUILD_COMPRESS = 'none'

# Whether to delete origin files when using compress, default false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# Whether to enable image compression
VITE_USE_IMAGEMIN= true

# use pwa
VITE_USE_PWA = false

# Is it compatible with older browsers
VITE_LEGACY = false