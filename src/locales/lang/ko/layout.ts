export default {
  footer: { onlinePreview: '미리보기', onlineDocument: '문서' },
  header: {
    // user dropdown
    dropdownItemDoc: '문서',
    dropdownItemLoginOut: '로그아웃',
    dropdownItemEditProfile: '프로필 편집',

    tooltipErrorLog: '오류 로그',
    tooltipNotify: '알림',

    tooltipEntryFull: '전체 화면',
    tooltipExitFull: '전체 화면 종료',

    home: '홈',
  },
  multipleTab: {
    reload: '현재 새로고침',
    close: '현재 닫기',
    closeLeft: '왼쪽 닫기',
    closeRight: '오른쪽 닫기',
    closeOther: '다른 탭 닫기',
    closeAll: '모든 탭 닫기',
  },
  setting: {
    // content mode
    contentModeFull: '전체',
    contentModeFixed: '고정 너비',
    // topMenu align
    topMenuAlignLeft: '왼쪽',
    topMenuAlignRight: '가운데',
    topMenuAlignCenter: '오른쪽',
    // menu trigger
    menuTriggerNone: '표시 안함',
    menuTriggerBottom: '하단',
    menuTriggerTop: '상단',
    // menu type
    menuTypeSidebar: '왼쪽 메뉴 모드',
    menuTypeMixSidebar: '왼쪽 메뉴 혼합 모드',
    menuTypeMix: '상단 메뉴 혼합 모드',
    menuTypeTopMenu: '상단 메뉴 모드',

    on: '켜기',
    off: '끄기',
    minute: '분',

    operatingTitle: '성공!',
    operatingContent:
      '복사가 성공했습니다. src/settings/projectSetting.ts로 가서 설정을 수정해주세요!',
    resetSuccess: '성공적으로 재설정되었습니다!',

    copyBtn: '복사',
    clearBtn: '캐시 지우고 로그인 페이지로',

    drawerTitle: '설정',

    darkMode: '다크 모드',
    navMode: '네비게이션 모드',
    interfaceFunction: '인터페이스 기능',
    interfaceDisplay: '인터페이스 표시',
    animation: '애니메이션',
    splitMenu: '분할 메뉴',
    closeMixSidebarOnChange: '페이지 전환 시 메뉴 닫기',

    sysTheme: '시스템 테마',
    headerTheme: '헤더 테마',
    sidebarTheme: '메뉴 테마',

    menuDrag: '사이드바 드래그',
    menuSearch: '메뉴 검색',
    menuAccordion: '사이드바 아코디언',
    menuCollapse: '메뉴 접기',
    collapseMenuDisplayName: '접힌 메뉴 이름 표시',
    topMenuLayout: '상단 메뉴 레이아웃',
    menuCollapseButton: '메뉴 접기 버튼',
    contentMode: '콘텐츠 영역 너비',
    expandedMenuWidth: '확장된 메뉴 너비',

    breadcrumb: '브레드크럼',
    breadcrumbIcon: '브레드크럼 아이콘',
    tabs: '탭',
    tabDetail: '탭 상세',
    tabsQuickBtn: '탭 빠른 버튼',
    tabsRedoBtn: '탭 다시 실행 버튼',
    tabsFoldBtn: '탭 접기 버튼',
    sidebar: '사이드바',
    header: '헤더',
    footer: '푸터',
    fullContent: '전체 콘텐츠',
    grayMode: '그레이 모드',
    colorWeak: '색약 모드',

    progress: '진행률',
    switchLoading: '스위치 로딩',
    switchAnimation: '스위치 애니메이션',
    animationType: '애니메이션 타입',

    fixedHeader: '고정 헤더',
    fixedSideBar: '고정 사이드바',

    mixSidebarTrigger: '혼합 메뉴 트리거',
    triggerHover: '호버',
    triggerClick: '클릭',

    mixSidebarFixed: '고정 확장 메뉴',
  },
}
