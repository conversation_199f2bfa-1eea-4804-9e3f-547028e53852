export default {
  charts: {
    baiduMap: '바이두 지도',
    aMap: 'A 지도',
    googleMap: '구글 지도',
    charts: '차트',
    map: '지도',
    line: '선형',
    pie: '파이',
  },
  comp: {
    comp: '컴포넌트',
    basic: '기본',
    transition: '애니메이션',

    scroll: '스크롤',
    scrollBasic: '기본',
    scrollAction: '스크롤 기능',
    virtualScroll: '가상 스크롤',

    tree: '트리',

    treeBasic: '기본',
    editTree: '검색 가능/툴바',
    actionTree: '기능 조작',

    modal: '모달',
    drawer: '드로어',
    desc: '설명',

    lazy: '지연 로딩',
    lazyBasic: '기본',
    lazyTransition: '애니메이션',

    verify: '검증',
    verifyDrag: '드래그',
    verifyRotate: '이미지 복원',

    strength: '비밀번호 강도',
    upload: '업로드',

    loading: '로딩',

    time: '상대 시간',
    cropperImage: '이미지 자르기',
  },
  editor: {
    editor: '에디터',
    jsonEditor: 'Json 에디터',
    markdown: '마크다운 에디터',

    tinymce: '리치 텍스트',
    tinymceBasic: '기본',
    tinymceForm: '임베디드 폼',
  },
  excel: {
    excel: '엑셀',
    customExport: '내보내기 형식 선택',
    jsonExport: 'JSON 데이터 내보내기',
    arrayExport: '배열 데이터 내보내기',
    importExcel: '가져오기',
  },
  feat: {
    feat: '페이지 기능',
    icon: '아이콘',
    tabs: '탭',
    tabDetail: '탭 상세',
    sessionTimeout: '세션 타임아웃',
    contextMenu: '컨텍스트 메뉴',
    download: '다운로드',
    imgPreview: '이미지 미리보기',
    copy: '클립보드',
    msg: '메시지 프롬프트',
    watermark: '워터마크',
    ripple: '리플',
    fullScreen: '전체 화면',
    errorLog: '오류 로그',
    tab: '매개변수가 있는 탭',
    tab1: '매개변수가 있는 탭 1',
    tab2: '매개변수가 있는 탭 2',
    menu: '매개변수가 있는 메뉴',
    menu1: '매개변수가 있는 메뉴 1',
    menu2: '매개변수가 있는 메뉴 2',

    breadcrumb: '브레드크럼',
    breadcrumbFlat: '플랫 모드',
    breadcrumbFlatDetail: '플랫 모드 상세',
    requestDemo: '재시도 요청 데모',

    breadcrumbChildren: '레벨 모드',
    breadcrumbChildrenDetail: '레벨 모드 상세',
  },
  flow: {
    name: '그래픽 에디터',
    flowChart: '플로우차트',
  },
  form: {
    form: '폼',
    basic: '기본',
    useForm: 'useForm',
    refForm: 'RefForm',
    advancedForm: '축소 가능',
    ruleForm: '폼 검증',
    dynamicForm: '동적',
    customerForm: '사용자 정의',
    appendForm: '추가',
    tabsForm: '탭 폼',
  },
  level: { level: '다중 메뉴' },
  page: {
    page: '페이지',

    form: '폼',
    formBasic: '기본 폼',
    formStep: '단계 폼',
    formHigh: '고급 폼',

    desc: '상세',
    descBasic: '기본 상세',
    descHigh: '고급 상세',

    result: '결과',
    resultSuccess: '성공',
    resultFail: '실패',

    account: '개인',
    accountCenter: '개인 센터',
    accountSetting: '개인 설정',

    exception: '예외',
    netWorkError: '네트워크 오류',
    notData: '데이터 없음',

    list: '목록 페이지',
    listCard: '카드 목록',
    basic: '기본 목록',
    listBasic: '기본 목록',
    listSearch: '검색 목록',
  },
  permission: {
    permission: '권한',

    front: '프론트엔드',
    frontPage: '페이지',
    frontBtn: '버튼',
    frontTestA: '테스트 페이지 A',
    frontTestB: '테스트 페이지 B',

    back: '백그라운드',
    backPage: '페이지',
    backBtn: '버튼',
  },
  setup: {
    page: '소개 페이지',
  },
  system: {
    moduleName: '시스템 관리',

    account: '계정 관리',
    account_detail: '계정 상세',
    password: '비밀번호 변경',

    dept: '부서 관리',

    menu: '메뉴 관리',
    role: '역할 관리',
  },
  table: {
    table: '테이블',

    basic: '기본',
    treeTable: '트리',
    fetchTable: '원격 로딩',
    fixedColumn: '고정 열',
    customerCell: '사용자 정의 열',
    formTable: '검색 열기',
    useTable: 'UseTable',
    refTable: 'RefTable',
    multipleHeader: '다중 레벨 헤더',
    mergeHeader: '셀 병합',
    expandTable: '확장 가능한 테이블',
    fixedHeight: '고정 높이',
    footerTable: '푸터',
    editCellTable: '편집 가능한 셀',
    editRowTable: '편집 가능한 행',
    authColumn: '인증 열',
    resizeParentHeightTable: 'resizeParentHeightTable',
  },
}
