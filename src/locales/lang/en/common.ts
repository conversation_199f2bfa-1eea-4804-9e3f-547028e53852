export default {
  okText: 'OK',
  closeText: 'Close',
  cancelText: 'Cancel',
  loadingText: 'Loading...',
  saveText: 'Save',
  delText: 'Delete',
  resetText: 'Reset',
  searchText: 'Search',
  queryText: 'Search',

  // Delete related texts
  deleteConfirmTitle: 'Are you sure you want to delete this user?',
  deleteSuccessMessage: 'User deleted successfully',
  deleteErrorMessage: 'Failed to delete user',
  yesText: 'Yes',
  noText: 'No',

  inputText: 'Please enter',
  chooseText: 'Please choose',

  redo: 'Refresh',
  back: 'Back',

  light: 'Light',
  dark: 'Dark',

  user: {
    detail: {
      title: 'User Detail: {username}',
    },
    status: {
      active: 'Active',
      deleted: 'Deleted',
      uploaded: 'Uploaded',
    },
    section: {
      basicInfo: 'Basic Information',
      verification: 'Verification & Status',
      assetVerification: 'Asset Verification',
      interests: 'Interests',
      photos: 'Photos',
      introduction: 'Introduction',
    },
    field: {
      provider: 'Login Provider',
      phone: 'Phone Number',
      nationality: 'Nationality',
      financialStatus: 'Financial Status',
      level: 'User Level',
      verificationStatus: 'Verification Status',
      verificationInfo: 'Verification Info',
      email: 'Email',
      idcard: 'ID Card',
      assetType: 'Asset Type',
      assetOwner: 'Asset Owner',
      assetProof: 'Asset Proof',
      assetVerification: 'Asset Verification',
    },
    action: {
      manageVerification: 'Manage Verification',
      manageFinancialStatus: 'Manage Financial Status',
      copyEmail: 'Copy Email',
      sendEmail: 'Send Email',
      preview: 'Preview',
    },
    financial: {
      notAuthorized: 'Not Authorized',
      submitted: 'Waiting for Review',
      risingStar: 'Rising Star (100M~500M KRW)',
      oneStar: '1 Star (500M~1B KRW)',
      twoStar: '2 Star (1B~3B KRW)',
      threeStar: '3 Star (3B~10B KRW)',
      fourStar: '4 Star (10B~30B KRW)',
      fiveStar: '5 Star (30B~50B KRW)',
      sixStar: '6 Star (50B~100B KRW)',
      sevenStar: '7 Star (100B+ KRW)',
      title: 'Financial Status & Asset Verification',
      updateSuccess: 'Financial status updated successfully',
      updateError: 'Failed to update financial status',
      updateErrorOccurred: 'An error occurred while updating financial status',
    },
    message: {
      emailCopied: 'Email copied to clipboard',
    },
    assetType: {
      RealEstate: 'Real Estate',
      FinancialAssets: 'Financial Assets',
      CryptoCurrency: 'Cryptocurrency',
      Others: 'Others',
    },
    assetOwner: {
      self: 'Self',
      spousal: 'Spousal',
      corporate: 'Corporate',
    },
    unit: {
      items: 'items',
      files: 'files',
    },
    certificate: {
      title: 'Certificate Files',
      spouse: 'Spouse Certificate:',
      corporate: 'Corporate Certificate:',
    },
    noAssets: 'No asset verification information available',
  },

  about: {
    title: 'About Starchex',
    subtitle: 'Information and Legal Documents',
    privacyPolicy: 'Privacy Policy',
    termsOfService: 'Terms of Service',
    contactUs: 'Contact Us',
    backToAdmin: '← Back to Admin Panel',
    comingSoon: 'page coming soon!',
  },

  privacy: {
    title: 'Privacy Policy',
    lastUpdated: 'Last updated: December 2024',
    back: '← Back',
    sections: {
      introduction: {
        title: '1. Introduction',
        content:
          'Welcome to Starchex Admin. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our administrative platform. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.',
      },
      informationWeCollect: {
        title: '2. Information We Collect',
        personalInfo: {
          title: '2.1 Personal Information',
          content:
            'We may collect personal information that you voluntarily provide to us when you:',
          items: [
            'Register for an account',
            'Log in to the platform',
            'Use administrative features',
            'Contact us for support',
          ],
        },
        usageInfo: {
          title: '2.2 Usage Information',
          content:
            'We automatically collect certain information when you access and use our platform:',
          items: [
            'Log data and usage statistics',
            'Device information',
            'IP address and location data',
            'Browser type and version',
          ],
        },
      },
      howWeUse: {
        title: '3. How We Use Your Information',
        content: 'We use the information we collect for various purposes, including:',
        items: [
          'Providing and maintaining our service',
          'Processing transactions and managing user accounts',
          'Improving our platform and user experience',
          'Communicating with you about updates and support',
          'Ensuring security and preventing fraud',
          'Complying with legal obligations',
        ],
      },
      informationSharing: {
        title: '4. Information Sharing and Disclosure',
        content:
          'We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except in the following circumstances:',
        items: [
          'With your explicit consent',
          'To comply with legal requirements',
          'To protect our rights and safety',
          'In connection with a business transfer',
        ],
      },
      dataSecurity: {
        title: '5. Data Security',
        content:
          'We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure.',
      },
      dataRetention: {
        title: '6. Data Retention',
        content:
          'We retain your personal information only for as long as necessary to fulfill the purposes outlined in this Privacy Policy, unless a longer retention period is required or permitted by law.',
      },
      yourRights: {
        title: '7. Your Rights',
        content:
          'Depending on your location, you may have the following rights regarding your personal information:',
        items: [
          'Right to access your personal data',
          'Right to rectify inaccurate data',
          'Right to erase your data',
          'Right to restrict processing',
          'Right to data portability',
          'Right to object to processing',
        ],
      },
      cookies: {
        title: '8. Cookies and Tracking Technologies',
        content:
          'We use cookies and similar tracking technologies to enhance your experience on our platform. You can control cookie settings through your browser preferences.',
      },
      changes: {
        title: '9. Changes to This Privacy Policy',
        content:
          'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.',
      },
      contact: {
        title: '10. Contact Information',
        content: 'If you have any questions about this Privacy Policy, please contact us:',
        email: 'Email: <EMAIL>',
        address: 'Address: [Your Company Address]',
        phone: 'Phone: [Your Contact Number]',
      },
    },
  },
}
