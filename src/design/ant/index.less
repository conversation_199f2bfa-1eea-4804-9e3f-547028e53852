@import './pagination.less';
@import './input.less';
@import './btn.less';

.ant-image-preview-root {
  img {
    display: unset;
  }
}

span.anticon:not(.app-iconify) {
  vertical-align: 0.125em !important;
}

.ant-back-top {
  right: 20px;
  bottom: 20px;
}

.collapse-container__body {
  > .ant-descriptions {
    margin-left: 6px;
  }
}

.ant-image-preview-operations {
  background-color: rgb(0 0 0 / 30%);
}

.ant-popover {
  &-content {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }
}

// =================================
// ==============modal message======
// =================================
.modal-icon-warning {
  color: @warning-color !important;
}

.modal-icon-success {
  color: @success-color !important;
}

.modal-icon-error {
  color: @error-color !important;
}

.modal-icon-info {
  color: @primary-color !important;
}

.ant-checkbox-checked .ant-checkbox-inner::after,
.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
  border-top: 0 !important;
  border-left: 0 !important;
}

.ant-form-item-control-input-content {
  > div {
    > div {
      max-width: 100%;
    }
  }
}
