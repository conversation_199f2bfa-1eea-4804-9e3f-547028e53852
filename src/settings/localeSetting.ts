import type { DropMenu } from '../components/Dropdown'
import type { LocaleSetting, LocaleType } from '/#/config'

export const LOCALE: { [key: string]: LocaleType } = {
  EN_US: 'en',
  KO_KR: 'ko',
  ZH_CN: 'zh_CN',
}

export const localeSetting: LocaleSetting = {
  showPicker: true,
  // Locale
  locale: LOCALE.EN_US,
  // Default locale
  fallback: LOCALE.EN_US,
  // available Locales
  availableLocales: [LOCALE.EN_US, LOCALE.KO_KR, LOCALE.ZH_CN],
}

// locale list
export const localeList: DropMenu[] = [
  {
    text: 'English',
    event: LOCALE.EN_US,
  },
  {
    text: '한국어',
    event: LOCALE.KO_KR,
  },
  {
    text: '简体中文',
    event: LOCALE.ZH_CN,
  },
]
