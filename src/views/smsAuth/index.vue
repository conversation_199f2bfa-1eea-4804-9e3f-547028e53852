<template>
  <PageWrapper title="SMS Auth Logs" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="username" label="Username">
                <a-input v-model:value="formState.username" placeholder="username" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        :scroll="{ x: 1400 }"
      >
        <a-table-column key="id" title="ID" data-index="id" :width="80" />
        <a-table-column key="user" title="User Name" data-index="user">
          <template #customRender="{ record }">
            <a-button type="link" @click="showDetail(record.user?.id, record.user?.username)">{{
              record.user?.username
            }}</a-button>
          </template>
        </a-table-column>
        <a-table-column key="code" title="Code" data-index="code">
          <template #customRender="{ record }">
            {{ record.code }}
          </template>
        </a-table-column>
        <a-table-column key="expiredAt" title="Expired Time" data-index="expiredAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.expiredAt) }}
          </template>
        </a-table-column>
        <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
      </a-table>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import { SmsAuthModel, SmsAuthSearchParams } from '/@/api/smsAuth/model/smsAuthModel'
  import { getSmsAuthLogs } from '/@/api/smsAuth/smsAuth'
  import { useGo } from '/@/hooks/web/usePage'
  const go = useGo()

  const isLoading = ref(false)
  const dataSource = ref<SmsAuthModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const formState = reactive({
    username: '',
    startDate: '',
    endDate: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: SmsAuthSearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      startDate: formState.startDate,
      endDate: formState.endDate,
      username: formState.username,
    }

    try {
      const res = await getSmsAuthLogs(params)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      isLoading.value = false
    }
  }

  const showDetail = (id, username): void => {
    go({
      name: 'user',
      params: { id, username },
    })
  }

  onMounted(() => {
    fetchData()
  })
</script>
<style scoped>
  :deep(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
