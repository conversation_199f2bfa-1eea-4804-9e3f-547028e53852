<template>
  <BasicModal
    v-bind="$attrs"
    width="50%"
    :wrapperFooterOffset="1"
    useWrapper:true
    @register="register"
    :title="isUpdate ? 'Update Post' : 'Add Post'"
    :ok-button-props="{ disabled: disabled }"
    ok-text="Save"
    @cancel="onReset"
    @ok="onSubmit"
  >
    <Card :bordered="false">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col :span="24">
          <a-card :bordered="false">
            <a-form layout="vertical" :model="formState">
              <a-form-item
                name="boardId"
                label="Board"
                :rules="[
                  {
                    required: true,
                    message: 'You have to pick a board',
                  },
                ]"
              >
                <a-select
                  v-model:value="formState.boardId"
                  show-search
                  :filter-option="filterOption"
                  :options="boards"
                />
              </a-form-item>

              <a-form-item
                label="Author"
                name="authorUser"
                :rules="[{ required: true, message: 'You have to pick a author' }]"
              >
                <a-select
                  v-model:value="formState.authorUser"
                  label-in-value
                  placeholder="search by username"
                  :filter-option="false"
                  :not-found-content="searchState.fetching ? undefined : null"
                  :options="searchState.data"
                  show-search
                  @search="fetchUser"
                >
                  <template v-if="searchState.fetching" #notFoundContent>
                    <a-spin size="small" />
                  </template>
                </a-select>
              </a-form-item>

              <a-form-item
                label="Content"
                name="content"
                :rules="[
                  {
                    required: true,
                    message: 'Post must have content.',
                  },
                ]"
              >
                <a-textarea
                  placeholder="Hi there, I am very happy"
                  :auto-size="{ minRows: 10 }"
                  v-model:value="formState.content"
                />
              </a-form-item>
              <!-- Preview Image -->
              <a-form-item label="Preview Image" name="previewImage">
                <div class="clearfix">
                  <a-upload
                    v-model:file-list="previewImageFileList"
                    accept="image/*"
                    name="files"
                    :headers="headers"
                    list-type="picture-card"
                    :action="uploadUrl"
                    @change="handlePreviewImageChange"
                    @preview="handlePreviewImagePreview"
                  >
                    <div v-if="previewImageFileList?.length < 1">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload Preview</div>
                    </div>
                  </a-upload>
                  <a-modal
                    :visible="previewImageVisible"
                    :footer="null"
                    @cancel="handlePreviewImageCancel"
                  >
                    <img alt="preview" style="width: 100%" :src="previewImageUrl" />
                  </a-modal>
                </div>
              </a-form-item>

              <a-form-item label="Media" name="image">
                <div class="clearfix">
                  <a-upload
                    v-model:file-list="fileList"
                    accept="image/*"
                    name="files"
                    :headers="headers"
                    list-type="picture-card"
                    :action="uploadUrl"
                    @change="handleChange"
                    @preview="handlePreview"
                  >
                    <div v-if="fileList?.length <= 8">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload</div>
                    </div>
                  </a-upload>
                  <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                    <img alt="example" style="width: 100%" :src="previewImage" />
                  </a-modal>
                </div>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
      </a-row>
    </Card>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { Card, UploadChangeParam, message } from 'ant-design-vue'
  import { onMounted, reactive, ref, watch } from 'vue'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { useGlobSetting } from '/@/hooks/setting'
  import { getToken } from '/@/utils/auth'
  import { getActiveBoards, getPost, updatePost, createPost } from '/@/api/community/community'
  import { PostDetailModel } from '/@/api/community/model/communityModels'
  import { getUsers } from '/@/api/users/users'

  import type { UploadProps } from 'ant-design-vue'
  import { debounce } from 'lodash-es'
  import { SearchParams } from '/@/api/users/model/userModel'

  const emit = defineEmits(['finished', 'register'])

  const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
    data && onDataReceive(data)
  })

  const postInfo = ref<PostDetailModel>()
  const boards = ref<{ label: string; value: number }[]>()
  const filterOption = (input: string, option: { label: string; value: number }) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  const onDataReceive = (data) => {
    if (data.id) {
      fetchData(data.id)
      isUpdate.value = true
    } else {
      isUpdate.value = false
      formState.boardId = 1
    }
  }

  const searchState = reactive<{ data: { label: string; value: number }[]; fetching: boolean }>({
    data: [],
    fetching: false,
  })

  const formState = reactive<any>({
    boardId: '',
    content: '',
    media: [],
    previewImage: null, // 添加预览图片字段
    authorUser: { key: '', label: '' },
  })

  const previewVisible = ref(false)
  const previewImage = ref('')
  const isUpdate = ref(true)

  // Preview Image 相关状态
  const previewImageFileList = ref<UploadProps['fileList']>([])
  const previewImageVisible = ref(false)
  const previewImageUrl = ref('')

  const headers = {
    Authorization: `Bearer ${getToken()}`,
  }
  const uploadUrl = useGlobSetting().apiUrl + '/upload'

  const fileList = ref<UploadProps['fileList']>([])

  function getBase64(file: File) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  const handlePreview = async (file: UploadProps['fileList'][number]) => {
    if (!file.url && !file.preview) {
      file.preview = (await getBase64(file.originFileObj)) as string
    }
    previewImage.value = file.url || file.preview
    previewVisible.value = true
  }

  const handleChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const id = info.file.response[0].id
      const uid = info.file.originFileObj?.uid
      const currentFile = info.fileList.find((file) => file.uid === uid)
      if (currentFile) {
        currentFile.uid = String(id)
      }
      message.success(`${info.file.name} file uploaded successfully`)
      verifyData()
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed.`)
    } else if (info.file.status === 'removed') {
      verifyData()
    }
  }

  const handleCancel = () => {
    previewVisible.value = false
  }
  const disabled = ref(true)
  const isLoading = ref(false)

  watch(formState, () => {
    verifyData()
  })

  const onSubmit = async () => {
    changeOkLoading(true)
    isLoading.value = true

    const params = {
      boardId: formState.boardId,
      content: formState.content,
      media: fileList.value
        ?.filter((file) => file.status == 'done')
        .map((file) => {
          return { id: file.uid }
        }),
      authorUser: { id: formState.authorUser.key },
    }
    try {
      isUpdate.value ? await updatePost(postInfo.value?.id ?? 0, params) : await createPost(params)
      isLoading.value = false
      message.success(`Successfully`)
      changeOkLoading(false)
      onReset()
      closeModal()
      emit('finished', {})
    } catch (error) {
      isLoading.value = false
    }
  }
  const onReset = async () => {
    formState.boardId = ''
    formState.content = ''
    fileList.value = []
  }

  const fetchData = async (id) => {
    try {
      const res = await getPost(Number(id))
      postInfo.value = res
      formState.boardId = res.board.id
      formState.content = res.content
      isLoading.value = false
      fileList.value = []
      formState.authorUser = { key: res.authorUser.id, label: res.authorUser.username }

      searchState.data = [
        { label: res.authorUser?.username ?? '', value: res.authorUser?.id ?? '' },
      ]

      res.media?.forEach((item) => {
        fileList.value?.push({
          uid: String(item.id),
          name: 'image.png',
          status: 'done',
          url: item.url,
          preview: item.url,
        })
      })
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }

  const fetchBoards = async () => {
    const res = await getActiveBoards()
    boards.value = res.map((item) => ({ label: item.description || item.name, value: item.id }))
  }

  const fetchUser = debounce(async (value) => {
    searchState.data = []
    searchState.fetching = true

    const params: SearchParams = {
      page: 1,
      pageSize: 10,
      username: value,
    }
    try {
      const res = await getUsers(params)
      const data = res.results.map((user) => ({
        label: user.username,
        value: user.id,
      }))
      searchState.data = data
      searchState.fetching = false
    } catch (error) {}
  }, 300)

  onMounted(() => {
    fetchBoards()
  })

  const verifyData = () => {
    const before = postInfo.value?.media
      ?.map((el) => String(el.id))
      .slice()
      .sort()
    const current = fileList.value
      ?.filter((item) => item.status == 'done')
      .map((el) => el.uid)
      .slice()
      .sort()

    console.log(formState)
    disabled.value =
      formState.boardId &&
      formState.content &&
      formState.boardId == postInfo.value?.board.id &&
      formState.content == postInfo.value?.content &&
      JSON.stringify(before) === JSON.stringify(current)
  }
</script>

<style scoped>
  .avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
  }

  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }
</style>
