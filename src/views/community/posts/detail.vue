<template>
  <PageWrapper title="Post" content="">
    <div class="mb-4 p-4 bg-white">
      <a-descriptions
        :title="`Post #${postInfo?.id ?? ''}`"
        :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }"
        bordered
      >
        <a-descriptions-item label="Board" :span="2">
          {{ postInfo?.board.description }}
        </a-descriptions-item>
        <a-descriptions-item label="Author(fakeName)" :span="2">
          <a-button
            type="link"
            @click="showUserDetail(postInfo?.authorUser ?? { id: 0, username: '', fakeName: '' })"
          >
            {{ `${postInfo?.authorUser.username} (${postInfo?.authorUser.fakeName})` }}</a-button
          >
        </a-descriptions-item>
        <a-descriptions-item label="Statistics" :span="2">
          <span class="mr-2"
            >Likes: <span class="text-red-500">{{ postInfo?.data.likeCount }}</span></span
          >
          <span class="mr-2"
            >Dislikes:
            <span class="text-red-500">{{ postInfo?.data.dislikeCount || 0 }}</span></span
          >
          <span class="mr-2"
            >Comments: <span class="text-red-500">{{ postInfo?.data.commentCount }}</span></span
          >
        </a-descriptions-item>

        <a-descriptions-item label="Status" :span="2">
          <a-tag color="red" v-if="postInfo?.removed">removed</a-tag>
          <a-tag color="red" v-else-if="postInfo?.blocked">blocked</a-tag>
          <a-tag color="cyan" v-else-if="postInfo?.pinned">pinned</a-tag>
          <a-tag color="green" v-else>active</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="Content" :span="4">
          <p style="white-space: pre-line">{{ postInfo?.content }}</p>
        </a-descriptions-item>
        <a-descriptions-item label="Media" :span="4">
          <div class="flex items-center overflow-hidden">
            <a-image-preview-group>
              <a-image
                style="object-fit: cover; height: 100%"
                :width="80"
                :height="80"
                v-for="image in postInfo?.media"
                :key="image.id"
                :src="image.formats?.webp?.url || image.url"
              />
            </a-image-preview-group>
          </div>
        </a-descriptions-item>
        <template #extra>
          <a-button class="mr-4" type="primary" @click="editPost()">Edit</a-button>
          <a-button
            class="mr-4"
            type="primary"
            @click="updatePost('blocked', postInfo)"
            :loading="isUpdating.blocked"
            >{{ postInfo?.blocked ? 'Unblock' : 'Block' }}</a-button
          >
          <a-button
            type="primary"
            @click="updatePost('pinned', postInfo)"
            :loading="isUpdating.pinned"
            >{{ postInfo?.pinned ? 'Unpin' : 'Pin' }}</a-button
          >
        </template>
      </a-descriptions>
    </div>
    <Card :bordered="false" title="Comments">
      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        size="small"
      >
        <a-table-column key="id" title="ID" data-index="id" />
        <a-table-column key="content" title="Content" data-index="content" />
        <a-table-column key="likeCount" title="Like" data-index="data">
          <template #customRender="{ text }">
            {{ text.likeCount }}
          </template>
        </a-table-column>
        <a-table-column key="dislikeCount" title="DisLike" data-index="data">
          <template #customRender="{ text }">
            {{ text.dislikeCount || 0 }}
          </template>
        </a-table-column>
        <a-table-column key="authorUser" title="Author (fakeName)" data-index="authorUser">
          <template #customRender="{ text }">
            <a-button type="link" @click="showUserDetail(text)">{{
              `${text.username} (${text.fakeName})`
            }}</a-button>
          </template>
        </a-table-column>

        <a-table-column key="status" title="Status" data-index="removed">
          <template #customRender="{ record }">
            <a-tag color="red" v-if="record.removed">removed</a-tag>
            <a-tag color="red" v-else-if="record.blocked">blocked</a-tag>
            <a-tag color="green" v-else>active</a-tag>
          </template>
        </a-table-column>

        <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
        <a-table-column key="operation" title="Action" fixed="right">
          <template #customRender="{ record }">
            <a-button
              type="link"
              size="small"
              @click="updateComment(record, 'blocked')"
              :loading="record.isUpdating.blocked"
              >{{ record.blocked ? 'Unblock' : 'Block' }}</a-button
            >
          </template>
        </a-table-column>
      </a-table>
    </Card>
    <PostModal @register="postModalRegister" @finished="onModalClosed" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { Card } from 'ant-design-vue'
  import { PageWrapper } from '/@/components/Page'
  import {
    getPost,
    updatePostStatus,
    getComments,
    updateCommentStatus,
  } from '/@/api/community/community'
  import { onMounted, ref } from 'vue'
  import {
    AuthorUser,
    PostDetailModel,
    CommentModel,
    CommentListParams,
  } from '/@/api/community/model/communityModels'
  import { useRoute } from 'vue-router'
  import { useGo } from '/@/hooks/web/usePage'
  import { useTabs } from '/@/hooks/web/useTabs'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import PostModal from './components/PostModal.vue'
  import { useModal } from '/@/components/Modal'

  const [postModalRegister, { openModal: openMessageModal }] = useModal()

  const { setTitle } = useTabs()
  interface ExCommentModel extends CommentModel {
    isUpdating: {
      blocked: boolean
    }
  }
  const go = useGo()
  const { id } = useRoute().params
  const isLoading = ref(true)
  const postInfo = ref<PostDetailModel>()
  const dataSource = ref<ExCommentModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const fetchData = async () => {
    try {
      const res = await getPost(Number(id))
      postInfo.value = res
      setTitle(`Post #${postInfo.value?.id ?? ''}`)
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    fetchData()
    fetchCommentsData()
  })

  const showUserDetail = (record: AuthorUser): void => {
    go({
      name: 'user',
      params: { id: record.id, username: record.username },
    })
  }

  const isUpdating = ref({
    blocked: false,
    pinned: false,
  })

  const updatePost = async (type: 'blocked' | 'pinned', record?: PostDetailModel) => {
    if (record) {
      try {
        isUpdating.value[type] = true
        const res = await updatePostStatus(postInfo.value?.id ?? 0, {
          [type]: !record[type],
        })
        if (res.ok) {
          record[type] = !record[type]
        }
        isUpdating.value[type] = false
      } catch (error) {
        isUpdating.value[type] = false
      }
    }
  }

  // const showCommentsList = () => {
  //   if ((postInfo.value?.data.commentCount ?? 0) > 0) {
  //     go({
  //       name: 'commentsList',
  //       params: { postId: postInfo.value?.id },
  //     })
  //   }
  // }

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchCommentsData()
  }

  const updateComment = async (record: ExCommentModel, type: 'blocked') => {
    try {
      record.isUpdating[type] = true
      const res = await updateCommentStatus(record.id, {
        blocked: !record.blocked,
      })
      if (res.ok) {
        record.blocked = !record[type]
      }
      record.isUpdating[type] = false
    } catch (error) {
      record.isUpdating[type] = false
    }
  }

  const fetchCommentsData = async () => {
    isLoading.value = true
    const params: CommentListParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      postId: Number(id),
    }

    try {
      const res = await getComments(params)
      dataSource.value = res.results.map((item): ExCommentModel => {
        const unit: ExCommentModel = {
          ...item,
          isUpdating: { blocked: false },
        }
        return unit
      })
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  const editPost = (): void => {
    openMessageModal(true, { id })
  }

  const onModalClosed = () => {
    fetchData()
  }
</script>
