<template>
  <PageWrapper title="Posts" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="board" label="Board">
                <a-select
                  v-model:value="formState.boardId"
                  show-search
                  :allowClear="true"
                  :filter-option="filterOption"
                  :options="boards"
                />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="authorName" label="Author Name">
                <a-input v-model:value="formState.authorName" placeholder="Author Name" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="blocked" label="Blocked">
                <a-select v-model:value="formState.blocked" :allowClear="true">
                  <a-select-option
                    v-for="item in blockOptions"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="pinned" label="Pinned">
                <a-select v-model:value="formState.pinned" :allowClear="true">
                  <a-select-option
                    v-for="item in pinedOptions"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="mb-4 p-4 bg-white">
        <div style="margin-bottom: 16px">
          <a-space wrap>
            <a-button type="primary" size="small" @click="onAdd"> New </a-button>
            <span style="margin-left: 8px">
              {{ `Total ${pagination.total} Posts` }}
            </span>
          </a-space>
        </div>
        <a-table
          :dataSource="dataSource"
          :pagination="pagination"
          @change="onChange"
          :loading="isLoading"
          size="small"
          :scroll="{ x: 1300 }"
        >
          <a-table-column key="id" title="ID" data-index="id" />

          <a-table-column key="content" title="Content" data-index="content">
            <template #customRender="{ text, record }">
              <a-button type="link" @click="showDetail(record)">{{ text }}</a-button>
            </template>
          </a-table-column>
          <a-table-column key="board" title="Board" data-index="board"
            ><template #customRender="{ text }">
              {{ text.description }}
            </template>
          </a-table-column>
          <a-table-column key="likeCount" title="Likes" data-index="data" align="center">
            <template #customRender="{ text }">
              {{ text.likeCount }}
            </template>
          </a-table-column>
          <a-table-column key="dislikeCount" title="DisLikes" data-index="data" align="center">
            <template #customRender="{ text }">
              {{ text.dislikeCount || 0 }}
            </template>
          </a-table-column>
          <a-table-column key="commentCount" title="Comments" data-index="data" align="center">
            <template #customRender="{ text, record }">
              <a-button
                v-if="text.commentCount > 0"
                type="link"
                @click="showCommentsList(record.id)"
                >{{ text.commentCount }}</a-button
              >
              <span v-else>{{ text.commentCount }}</span>
            </template>
          </a-table-column>

          <a-table-column key="authorUser" title="Author (fakeName)" data-index="authorUser">
            <template #customRender="{ text }">
              <a-button type="link" @click="showUserDetail(text)">{{
                `${text.username} (${text.fakeName})`
              }}</a-button>
            </template>
          </a-table-column>

          <a-table-column key="status" title="Status" data-index="removed">
            <template #customRender="{ record }">
              <a-tag color="red" v-if="record.removed">removed</a-tag>
              <a-tag color="red" v-if="record.blocked">blocked</a-tag>
              <a-tag color="cyan" v-if="record.pinned">pinned</a-tag>
            </template>
          </a-table-column>

          <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
            <template #customRender="{ record }">
              {{ formatToDateTime(record.createdAt) }}
            </template>
          </a-table-column>
          <a-table-column key="operation" title="">
            <template #customRender="{ record }">
              <a-button
                type="link"
                size="small"
                @click="updatePost(record, 'blocked')"
                :loading="record.isUpdating.blocked"
                >{{ record.blocked ? 'Unblock' : 'Block' }}</a-button
              >
              <a-button
                type="link"
                size="small"
                @click="updatePost(record, 'pinned')"
                :loading="record.isUpdating.pinned"
                >{{ record.pinned ? 'Unpin' : 'Pin' }}</a-button
              >
            </template>
          </a-table-column>
        </a-table>
      </div>
    </div>
    <PostModal @register="postModalRegister" @finished="onModalClosed" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { getPosts, updatePostStatus, getActiveBoards } from '/@/api/community/community'
  import { PostModel, PostListParams } from '/@/api/community/model/communityModels'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import { UserModel } from '/@/api/users/model/userModel'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import PostModal from './components/PostModal.vue'
  import { useModal } from '/@/components/Modal'

  const [postModalRegister, { openModal: openMessageModal }] = useModal()

  interface ExPostModel extends PostModel {
    isUpdating: {
      blocked: boolean
      pinned: boolean
    }
  }

  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<ExPostModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showUserDetail = (record: UserModel): void => {
    go({
      name: 'user',
      params: { id: record.id, username: record.username },
    })
  }

  const formState = reactive({
    boardId: '',
    authorName: '',
    blocked: '',
    pinned: '',
    startDate: '',
    endDate: '',
  })

  const boards = ref<{ label: string; value: number }[]>()
  const filterOption = (input: string, option: { label: string; value: number }) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const blockOptions = ref<{ label: string; value: number }[]>([
    { label: 'Blocked', value: 1 },
    { label: 'Unblock', value: 0 },
  ])

  const pinedOptions = ref<{ label: string; value: number }[]>([
    { label: 'Pinned', value: 1 },
    { label: 'Unpin', value: 0 },
  ])

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: PostListParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      authorName: formState.authorName,
      pinned: formState.pinned == '' ? undefined : Number(formState.pinned),
      blocked: formState.blocked == '' ? undefined : Number(formState.blocked),
      startDate: formState.startDate,
      endDate: formState.endDate,
      boardId:
        formState.boardId === undefined || formState.boardId == ''
          ? undefined
          : Number(formState.boardId),
    }

    try {
      const res = await getPosts(params)
      dataSource.value = res.results.map((item): ExPostModel => {
        const content = item.content.length > 60 ? item.content.slice(0, 60) + '...' : item.content
        const unit: ExPostModel = {
          ...item,
          content,
          isUpdating: { blocked: false, pinned: false },
        }
        return unit
      })
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }

  const fetchBoards = async () => {
    const res = await getActiveBoards()
    boards.value = res.map((item) => ({ label: item.description || item.name, value: item.id }))
  }
  fetchBoards()

  onMounted(() => {
    fetchData()
  })

  const updatePost = async (record: ExPostModel, type: 'blocked' | 'pinned') => {
    try {
      record.isUpdating[type] = true
      const res = await updatePostStatus(record.id, {
        [type]: !record[type],
      })
      if (res.ok) {
        record[type] = !record[type]
      }
      record.isUpdating[type] = false
    } catch (error) {
      record.isUpdating[type] = false
    }
  }

  const showDetail = (record: ExPostModel) => {
    go({
      name: 'postDetail',
      params: { id: record.id },
    })
  }

  const showCommentsList = (id: number) => {
    go({
      name: 'commentsList',
      params: { postId: id },
    })
  }

  const onAdd = (): void => {
    openMessageModal(true, {})
  }

  const onModalClosed = () => {
    fetchData()
  }
</script>
<style scoped>
  .table-image :deep(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
