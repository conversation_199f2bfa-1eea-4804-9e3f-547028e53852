<template>
  <PageWrapper title="Comments" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="authorName" label="Author Name">
                <a-input v-model:value="formState.authorName" placeholder="Author Name" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="postId" label="Post ID">
                <a-input v-model:value="formState.postId" placeholder="Post ID" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="blocked" label="Blocked">
                <a-select v-model:value="formState.blocked" :allowClear="true">
                  <a-select-option
                    v-for="item in blockOptions"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        size="small"
        :scroll="{ x: 1300 }"
      >
        <a-table-column key="id" title="ID" data-index="id" />

        <a-table-column key="postId" title="Post ID" data-index="post">
          <template #customRender="{ text }">
            <a-button type="link" @click="showPostDetail(text)">{{ `#${text.id}` }}</a-button>
          </template>
        </a-table-column>
        <a-table-column key="content" title="Content" data-index="content" />
        <a-table-column key="likeCount" title="Like" data-index="data">
          <template #customRender="{ text }">
            {{ text.likeCount }}
          </template>
        </a-table-column>
        <a-table-column key="dislikeCount" title="DisLike" data-index="data">
          <template #customRender="{ text }">
            {{ text.dislikeCount || 0 }}
          </template>
        </a-table-column>
        <a-table-column key="authorUser" title="Author (fakeName)" data-index="authorUser">
          <template #customRender="{ text }">
            <a-button type="link" @click="showUserDetail(text)">{{
              `${text.username} (${text.fakeName})`
            }}</a-button>
          </template>
        </a-table-column>

        <a-table-column key="status" title="Status" data-index="removed">
          <template #customRender="{ record }">
            <a-tag color="red" v-if="record.removed">removed</a-tag>
            <a-tag color="red" v-if="record.blocked">blocked</a-tag>
          </template>
        </a-table-column>

        <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
        <a-table-column key="operation" title="">
          <template #customRender="{ record }">
            <a-button
              type="link"
              size="small"
              @click="updateComment(record, 'blocked')"
              :loading="record.isUpdating.blocked"
              >{{ record.blocked ? 'Unblock' : 'Block' }}</a-button
            >
          </template>
        </a-table-column>
      </a-table>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { getComments, updateCommentStatus } from '/@/api/community/community'
  import { CommentModel, CommentListParams } from '/@/api/community/model/communityModels'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import { UserModel } from '/@/api/users/model/userModel'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import { useRoute } from 'vue-router'

  interface ExCommentModel extends CommentModel {
    isUpdating: {
      blocked: boolean
    }
  }

  const { postId } = useRoute().params
  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<ExCommentModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    position: ['bottomCenter'],
  })

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showUserDetail = (record: UserModel): void => {
    go({
      name: 'user',
      params: { id: record.id, username: record.username },
    })
  }

  const formState = reactive({
    authorName: '',
    postId: '',
    blocked: '',
    startDate: '',
    endDate: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const blockOptions = ref<{ label: string; value: number }[]>([
    { label: 'Blocked', value: 1 },
    { label: 'Unblock', value: 0 },
  ])

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: CommentListParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      authorName: formState.authorName,
      blocked: formState.blocked == '' ? undefined : Number(formState.blocked),
      postId: formState.postId == '' ? undefined : Number(formState.postId),
      startDate: formState.startDate,
      endDate: formState.endDate,
    }

    try {
      const res = await getComments(params)
      dataSource.value = res.results.map((item): ExCommentModel => {
        const unit: ExCommentModel = {
          ...item,
          isUpdating: { blocked: false },
        }
        return unit
      })
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    if (postId) {
      formState.postId = postId.toString()
    }
    fetchData()
  })

  const updateComment = async (record: ExCommentModel, type: 'blocked') => {
    try {
      record.isUpdating[type] = true
      const res = await updateCommentStatus(record.id, {
        blocked: !record.blocked,
      })
      if (res.ok) {
        record.blocked = !record[type]
      }
      record.isUpdating[type] = false
    } catch (error) {
      record.isUpdating[type] = false
    }
  }

  const showPostDetail = (record: ExCommentModel) => {
    go({
      name: 'postDetail',
      params: { id: record.id },
    })
  }
</script>
<style scoped>
  .table-image :deep(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
