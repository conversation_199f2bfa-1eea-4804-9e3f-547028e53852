<template>
  <PageWrapper>
    <Card title="Daily Given Card Setting" :bordered="false">
      <a-form
        ref="formRef"
        name="dailyRules"
        :model="dynamicValidateForm"
        v-bind="formItemLayoutWithOutLabel"
      >
        <a-form-item
          v-for="(rule, index) in dynamicValidateForm.rules"
          :key="rule.key"
          v-bind="index === 0 ? formItemLayout : {}"
          :label="index === 0 ? 'Rules' : ''"
          :name="['rules', index, 'hour']"
          :rules="{
            required: true,
            message: 'Time cannot be empty.',
            trigger: 'change',
          }"
        >
          <a-input-number
            style="width: 50%"
            id="inputNumber"
            placeholder="please input hour"
            v-model:value="rule.hour"
            :min="0"
            :max="23"
          >
            <template #addonAfter>
              <delete-two-tone
                v-if="dynamicValidateForm.rules.length >= 1"
                two-tone-color="red"
                class="dynamic-delete-button"
                @click="removeRule(rule)"
            /></template>
          </a-input-number>

          <!-- <a-input v-model:value="rule.hour" placeholder="please input hour" /> -->
        </a-form-item>
        <a-form-item v-bind="formItemLayoutWithOutLabel">
          <a-button type="dashed" style="width: 50%" @click="addRule">
            <PlusOutlined />
            Add Card
          </a-button>
        </a-form-item>
        <a-form-item
          :wrapper-col="{ span: 14, offset: 4 }"
          style="display: flex; justify-content: center; margin-top: 80px"
        >
          <a-popconfirm
            placement="topLeft"
            title="Are you sure update this rule?"
            ok-text="Yes"
            cancel-text="No"
            @confirm="submitForm"
          >
            <a-button type="primary" :disabled="disabled"> Submit </a-button>
          </a-popconfirm>
          <a-button style="margin-left: 10px" :disabled="disabled" @click="onReset">Reset</a-button>
        </a-form-item>
      </a-form>
    </Card>
    <template #rightFooter> </template>
  </PageWrapper>
</template>
<script lang="ts" setup name="dailySetting">
  import { PageWrapper } from '/@/components/Page'
  import { Card, message, Popconfirm } from 'ant-design-vue'
  import { onMounted, reactive, ref, watch } from 'vue'
  import { PlusOutlined, DeleteTwoTone } from '@ant-design/icons-vue'
  import { getDailyRule, updateDailyRule } from '/@/api/daily/daily'
  import type { FormInstance } from 'ant-design-vue'
  import { isEqual } from 'lodash-es'

  const APopconfirm = Popconfirm

  const disabled = ref(true)

  interface DailyRule {
    hour: number
    key: number
  }
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 4 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 20 },
    },
  }
  const formRef = ref<FormInstance>()

  const formItemLayoutWithOutLabel = {
    wrapperCol: {
      xs: { span: 24, offset: 0 },
      sm: { span: 20, offset: 4 },
    },
  }
  const dynamicValidateForm = reactive<{ rules: DailyRule[]; initial: DailyRule[] }>({
    rules: [],
    initial: [],
  })

  const submitForm = async () => {
    try {
      const values = dynamicValidateForm.rules.map((item) => item.hour)
      await updateDailyRule(values)
      message.success(`Update Successfully`)
      dynamicValidateForm.initial = [...dynamicValidateForm.rules]
    } catch (error) {
      message.error('Woops! Something went wrong. Please, try again.')
    }
  }

  const onReset = async () => {
    dynamicValidateForm.rules = [...dynamicValidateForm.initial]
  }

  const removeRule = (item: DailyRule) => {
    const index = dynamicValidateForm.rules.indexOf(item)
    if (index !== -1) {
      dynamicValidateForm.rules.splice(index, 1)
    }
  }
  const addRule = () => {
    dynamicValidateForm.rules.push({
      hour: 0,
      key: Date.now(),
    })
  }

  onMounted(async () => {
    try {
      const res = await getDailyRule()
      const rules = res.map((value, index) => {
        return {
          hour: Number(value),
          key: index,
        }
      })
      dynamicValidateForm.rules = [...rules]
      dynamicValidateForm.initial = [...rules]
    } catch (error) {}
  })

  watch(dynamicValidateForm, () => {
    disabled.value = isEqual(dynamicValidateForm.rules, dynamicValidateForm.initial)
  })
</script>
<style scoped>
  .dynamic-delete-button {
    cursor: pointer;
    position: relative;
    top: 4px;
    font-size: 20px;
    transition: all 0.3s;
  }

  .dynamic-delete-button:hover {
    color: #777;
  }

  .dynamic-delete-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
  }
</style>
