<template>
  <PageWrapper title="<PERSON> opens record" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="username" label="User Name">
                <a-input
                  v-model:value="formState.username"
                  :allowClear="true"
                  placeholder="User name"
                />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="cardname" label="Card Name">
                <a-input
                  v-model:value="formState.cardname"
                  :allowClear="true"
                  placeholder="Card Name"
                />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="cardtype" label="Card Type">
                <a-select v-model:value="formState.type" :allowClear="true">
                  <a-select-option
                    v-for="item in cardTypes"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        size="small"
      >
        <a-table-column key="user" title="User Name" data-index="recommended">
          <template #customRender="{ record }">
            <a-popover>
              <template #content>
                <a-space>
                  <a-button
                    type="link"
                    @click="showDetail(record.recommended?.id, record.recommended?.username)"
                    >Profile</a-button
                  >
                  <a-button type="link" @click="searchUser(record.recommended?.username)"
                    >Search</a-button
                  >
                </a-space>
              </template>
              <a-button type="link">{{ record.recommended?.username }}</a-button>
            </a-popover>
          </template>
        </a-table-column>

        <a-table-column key="cardtype" title="Card Type" data-index="type">
          <template #customRender="{ record }">
            <a-tag :color="getCarTypeColor(record.type)">{{ getCardTypeTitle(record.type) }}</a-tag>
          </template>
        </a-table-column>

        <a-table-column key="source" title="Source" data-index="diffInDays">
          <template #customRender="{ record }">
            <a-tag :color="getSourceColor(record)">{{ getSourceText(record) }}</a-tag>
          </template>
        </a-table-column>

        <a-table-column key="photos" title="Card Info" data-index="recommender">
          <template #customRender="{ record }">
            <div class="space-align-block">
              <a-space align="center">
                <div
                  class="table-image mx-auto transform text-center hover:scale-110 duration-300 ease-in-out overflow-hidden w-50px h-50px"
                >
                  <a-image
                    :width="50"
                    :height="50"
                    :src="
                      record.recommender?.avatar?.formats?.webp?.url ||
                      record.recommender?.avatar?.url
                    "
                  />
                </div>
                <a-button
                  type="link"
                  @click="showDetail(record.recommender?.id, record.recommender?.username)"
                  >{{ record.recommender?.username }}</a-button
                >
              </a-space>
            </div>
          </template>
        </a-table-column>
        <a-table-column key="cardlevel" title="Card Lvel" data-index="recommender">
          <template #customRender="{ record }">
            <a-tag :color="getLevelColor(record.recommender.level)">{{
              getLevelText(record.recommender.level)
            }}</a-tag>
          </template>
        </a-table-column>

        <a-table-column key="createdAt" title="Open Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
      </a-table>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup name="dailyList">
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import { RecommendationModel, RecommendationSearchParams } from '/@/api/daily/model/dailyModel'
  import { getRecommendations } from '/@/api/daily/daily'

  const cardTypes = ref<{ label: string; value: string; color: string }[]>([
    { label: 'Given Card', value: 'standard', color: 'purple' },
    { label: 'Premium Card', value: 'premium', color: 'volcano' },
  ])

  const levelTypes = ref<{ label: string; shortLabel: string; value: number; color: string }[]>([
    { label: 'best, premium card pool', value: 5, shortLabel: 'best', color: '#FC9E05' },
    { label: 'very good, premium card pool', value: 4, shortLabel: 'very good', color: '#BF05FA' },
    {
      label: 'not bad, normal, in given card',
      value: 3,
      shortLabel: 'not bad',
      color: '#089FFA',
    },
    {
      label: 'so so, not his/her actual picture, in given card',
      value: 2,
      shortLabel: 'so so',
      color: '#0BE153',
    },
    {
      label: 'no recommend, picture has problem - do not show in given card',
      value: 1,
      shortLabel: 'no recommend',
      color: '#d2d2d2',
    },
  ])

  const getCarTypeColor = (type) => {
    return cardTypes.value.find((el) => el.value == type)?.color
  }

  const getCardTypeTitle = (type) => {
    return cardTypes.value.find((el) => el.value == type)?.label
  }

  const getLevelColor = (level) => {
    return levelTypes.value.find((el) => el.value == level)?.color
  }

  const getLevelText = (level) => {
    return levelTypes.value.find((el) => el.value == level)?.shortLabel
  }

  const getSourceColor = (record) => {
    if (record.type === 'premium') {
      return 'orange'
    } else {
      if (record.diffInDays == 0) {
        return 'green'
      } else {
        return 'red'
      }
    }
  }

  const getSourceText = (record) => {
    if (record.type === 'premium') {
      return 'Premium'
    } else {
      if (record.diffInDays == 0) {
        return 'Today'
      } else {
        return `D-${7 - Math.abs(record.diffInDays)}`
      }
    }
  }

  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<RecommendationModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showDetail = (id, username): void => {
    go({
      name: 'user',
      params: { id, username },
    })
  }

  const searchUser = (username): void => {
    formState.username = username
    pagination.value.current = 1
    fetchData()
  }

  const formState = reactive({
    username: '',
    cardname: '',
    startDate: '',
    endDate: '',
    type: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: RecommendationSearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      username: formState.username,
      cardname: formState.cardname,
      startDate: formState.startDate,
      endDate: formState.endDate,
      type: formState.type,
    }

    try {
      const res = await getRecommendations(params)
      console.log(res)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    fetchData()
  })
</script>
