<template>
  <PageWrapper title="Users" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="action" label="Action">
                <a-select v-model:value="formState.action" :allowClear="true">
                  <a-select-option v-for="item in actions" :value="item.value" :key="item.value">{{
                    item.label
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="processed" label="Processed Type">
                <a-select v-model:value="formState.processed" :allowClear="true">
                  <a-select-option
                    v-for="item in processedType"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        size="small"
        :scroll="{ x: 1300 }"
      >
        <a-table-column key="reported" title="Reported" data-index="reported">
          <template #customRender="{ record }">
            <a-button
              type="link"
              @click="showDetail(record.reported.id, record.reported.username)"
              >{{ record.reported.username }}</a-button
            >
          </template>
        </a-table-column>

        <a-table-column key="reporter" title="Reporter" data-index="reporter">
          <template #customRender="{ record }">
            <a-button
              type="link"
              @click="showDetail(record.reporter.id, record.reporter.username)"
              >{{ record.reporter.username }}</a-button
            >
          </template>
        </a-table-column>

        <a-table-column key="reasons" title="Reasons" data-index="reasons">
          <template #customRender="{ record }">
            <p v-for="reason in record.reasons" :key="reason.type">{{
              `${reason.type}: ${reason.text}`
            }}</p>
          </template>
        </a-table-column>

        <a-table-column key="action" title="Action" data-index="action">
          <template #customRender="{ record }">
            <p>{{ record.action ? record.action : '--' }}</p>
          </template>
        </a-table-column>
        <a-table-column
          key="attachments"
          title="Attachments"
          data-index="attachments"
          align="center"
        >
          <template #customRender="{ record }">
            <div class="flex items-center">
              <div
                class="mr-1 transform hover:scale-110 duration-300 ease-in-out"
                v-for="image in record?.attachments"
                :key="image.id"
              >
                <a-image
                  class.native="transform hover:scale-105 duration-300 ease-linear"
                  :width="50"
                  :src="image.formats?.webp?.url || image.url"
                />
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
        <a-table-column key="operation" data-index="operation">
          <template #customRender="{ record }">
            <a-button
              v-if="record.processed != ReportProcessed.handled"
              @click="onClickAction(record)"
              type="primary"
              >Action</a-button
            >
          </template>
        </a-table-column>
      </a-table>
    </div>
    <ReportModal @register="reportModalRegister" @finished="onHandled" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import { getReports } from '/@/api/report/report'
  import {
    ReportSearchParams,
    Report,
    ReportAction,
    ReportProcessed,
  } from '/@/api/report/model/reportModel'
  import ReportModal from './components/ReportModal.vue'
  import { useModal } from '/@/components/Modal/src/hooks/useModal'

  const [reportModalRegister, { openModal: openReportModal }] = useModal()
  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<Report[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const actions = ref<{ label: string; value: ReportAction }[]>(
    Object.keys(ReportAction).map((element) => {
      return { label: element, value: ReportAction[element] }
    }),
  )

  const processedType = ref<{ label: string; value: ReportProcessed }[]>([
    { label: 'Unhandled', value: ReportProcessed.unhandled },
    { label: 'Handled', value: ReportProcessed.handled },
  ])

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showDetail = (id, username): void => {
    go({
      name: 'user',
      params: { id, username },
    })
  }

  const formState = reactive({
    action: '',
    processed: '',
    startDate: '',
    endDate: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: ReportSearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      action: formState.action,
      processed: formState.processed === '' ? null : Number(formState.processed),
      startDate: formState.startDate,
      endDate: formState.endDate,
    }

    try {
      const res = await getReports(params)
      console.log(res)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    fetchData()
  })

  const onClickAction = (record: Report) => {
    console.log(record)
    openReportModal(true, {
      id: record.id,
    })
  }

  const onHandled = () => {
    console.log('onHandled')
    fetchData()
  }
</script>
