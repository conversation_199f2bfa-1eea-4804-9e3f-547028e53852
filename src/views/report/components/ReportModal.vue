<template>
  <BasicModal v-bind="$attrs" @register="register" title="Report Handle" @ok="onClickOk">
    <div class="pt-3px pr-3px">
      <a-form ref="formRef" :model="formState">
        <a-form-item name="action" label="Action">
          <a-select v-model:value="formState.action" :allowClear="true">
            <a-select-option v-for="item in actions" :value="item.value" :key="item.value">{{
              item.label
            }}</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item v-if="isShowTempBanDuration" name="tempBanDuration" label="Duration (Days)">
          <a-input-number v-model:value="formState.tempBanDuration" :min="1" :precision="0" />
        </a-form-item>

        <a-form-item name="note" label="Note">
          <a-textarea
            v-model:value="formState.note"
            placeholder="Please input note"
            :auto-size="{ minRows: 2, maxRows: 5 }"
          />
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue'
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import type { FormInstance } from 'ant-design-vue'
  import { ReportAction } from '/@/api/report/model/reportModel'
  import { handleReport } from '/@/api/report/report'

  const actions = ref<{ label: string; value: ReportAction }[]>(
    Object.keys(ReportAction).map((element) => {
      return { label: element, value: ReportAction[element] }
    }),
  )
  const formRef = ref<FormInstance>()

  const formState = reactive({
    action: '' as ReportAction,
    note: '',
    tempBanDuration: 1,
  })

  type propsData = {
    id: number
    title?: string
    balance?: number
  }

  const emit = defineEmits(['finished', 'register'])

  const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
    data && onDataReceive(data)
  })

  const isShowTempBanDuration = computed(() => formState.action === ReportAction.tempBan)

  const receivedData = ref<propsData>()
  const onDataReceive = (data: propsData) => {
    console.log('onDataReceive', data)
    receivedData.value = data
  }
  const onClickOk = async () => {
    changeOkLoading(true)
    try {
      const res = await handleReport(
        receivedData.value!.id,
        formState.action,
        formState.note,
        formState.tempBanDuration,
      )
      console.log('res', res)
      changeOkLoading(false)
      if (res.ok) {
        closeModal()
        emit('finished')
      }
    } catch (error) {
      changeOkLoading(false)
    }
  }
</script>
