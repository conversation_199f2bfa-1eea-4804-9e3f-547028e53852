<template>
  <BasicModal
    v-bind="$attrs"
    width="90%"
    :wrapperFooterOffset="1"
    useWrapper:true
    @register="register"
    title="New Push Message"
    :ok-button-props="{ disabled: disabled }"
    ok-text="Send"
    @cancel="onReset"
    @ok="onSubmit"
  >
    <Card title="1. Audience" :bordered="false">
      <a-form :model="formState" layout="vertical">
        <a-form-item>
          <a-radio-group v-model:value="formState.audienceType">
            <a-radio :style="radioStyle" value="all">Send to Total Users</a-radio>
            <a-radio :style="radioStyle" value="segment">Send to particular users </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="formState.audienceType === 'segment'"
          label="Which users should receive this message?"
        >
          <a-select
            v-model:value="formState.segments"
            mode="multiple"
            label-in-value
            placeholder="search by username"
            style="width: 58%"
            :filter-option="false"
            :not-found-content="searchState.fetching ? undefined : null"
            :options="searchState.data"
            @search="fetchUser"
          >
            <template v-if="searchState.fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
          </a-select>
        </a-form-item>
      </a-form>
    </Card>
    <Card title="2. Message" :bordered="false">
      <a-row :gutter="{ xs: 8, sm: 16, md: 24, lg: 32 }">
        <a-col :span="14">
          <a-card :bordered="false">
            <a-form layout="vertical" :model="formState">
              <a-form-item label="Title" name="title">
                <a-input placeholder="Title" v-model:value="formState.title" />
              </a-form-item>

              <a-form-item
                label="Message"
                name="content"
                :rules="[
                  {
                    required: true,
                    message: 'Notifications must have message',
                  },
                ]"
              >
                <a-textarea
                  placeholder="Message"
                  :auto-size="{ minRows: 3 }"
                  v-model:value="formState.content"
                />
              </a-form-item>
              <a-form-item label="Image" name="image">
                <div class="clearfix">
                  <a-upload
                    v-model:file-list="fileList"
                    accept="image/*"
                    name="files"
                    :headers="headers"
                    list-type="picture-card"
                    :action="uploadUrl"
                    @remove="handleRemove"
                    @change="handleChange"
                    @preview="handlePreview"
                  >
                    <div v-if="fileList.length < 1">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload</div>
                    </div>
                  </a-upload>
                  <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                    <img alt="example" style="width: 100%" :src="formState.imageUrl" />
                  </a-modal>
                </div>
              </a-form-item>
              <a-form-item
                label="Launch URL"
                name="url"
                :rules="[
                  {
                    pattern: /^(https?:\/\/).*/,
                    message: 'Please enter a valid URL starting with http:// or https://',
                  },
                ]"
              >
                <a-input placeholder="https://" v-model:value="formState.link" />
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
        <a-col :span="10"
          ><div class="preivew-warp">
            <div class="preview-card">
              <div class="preview-box">
                <a-row style="margin-bottom: 12px">
                  <a-col flex="30px"
                    ><img
                      src="../../../assets/images/logo.png"
                      style="object-fit: cover; width: 18px; border-radius: 18px"
                  /></a-col>
                  <a-col flex="auto"><span>Meetok • now</span></a-col>
                  <a-col><UpOutlined /></a-col>
                </a-row>
                <a-row :wrap="false" v-if="formState.title">
                  <a-col flex="30px" />
                  <a-col flex="auto">
                    <span style="font-weight: bold">{{ formState.title }}</span></a-col
                  >
                </a-row>
                <a-row :wrap="false" style="margin-top: 4px">
                  <a-col flex="30px" />
                  <a-col flex="auto"
                    ><span>{{
                      formState.content ? formState.content : 'Default message'
                    }}</span></a-col
                  >
                </a-row>
                <a-row :wrap="false" style="margin-top: 8px">
                  <a-col flex="30px" />
                  <a-col flex="auto">
                    <img
                      v-if="formState.imageUrl"
                      :src="formState.imageUrl"
                      alt="avatar"
                      class="preview-image"
                    />
                  </a-col> </a-row></div
            ></div> </div
        ></a-col>
      </a-row>
    </Card>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { Card, UploadChangeParam, message } from 'ant-design-vue'
  import { reactive, ref, watch } from 'vue'
  import { PlusOutlined, UpOutlined } from '@ant-design/icons-vue'
  import { debounce } from 'lodash-es'
  import { getUsers } from '/@/api/users/users'
  import { SearchParams } from '/@/api/users/model/userModel'
  import { useGlobSetting } from '/@/hooks/setting'
  import { getToken } from '/@/utils/auth'
  import { postMessage } from '/@/api/messages/messages'
  import { MessageModel, SegmentModel } from '/@/api/messages/model/messageModel'
  const emit = defineEmits(['finished', 'register'])

  const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
    data && onDataReceive(data)
  })
  const onDataReceive = (data) => {
    formState.audienceType = data.audienceType
    const users = data.selectedUsers.map((user) => ({
      key: user.id,
      value: user.id,
      label: user.username,
    }))
    formState.segments = users
    searchState.data = users
  }

  const formState = reactive<any>({
    audienceType: 'all',
    segments: [],
    title: '',
    content: '',
    imageUrl: '',
    link: '',
  })

  const previewVisible = ref(false)

  const headers = {
    Authorization: `Bearer ${getToken()}`,
  }
  const uploadUrl = useGlobSetting().apiUrl + '/upload'

  const fileList = ref([])

  const handleRemove = () => {
    formState.imageUrl = ''
  }
  const loading = ref<boolean>(false)

  const handleChange = (info: UploadChangeParam) => {
    if (info.file.status !== 'uploading') {
      loading.value = true
    }
    if (info.file.status === 'done') {
      loading.value = false
      const url = info.file.response[0].url
      formState.imageUrl = url
      message.success(`${info.file.name} file uploaded successfully`)
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed.`)
    }
  }

  const handlePreview = () => {
    previewVisible.value = true
  }

  const handleCancel = () => {
    previewVisible.value = false
  }
  const disabled = ref(true)
  const isLoading = ref(false)

  const radioStyle = reactive({})

  const searchState = reactive<{ data: { label: string; value: number }[]; fetching: boolean }>({
    data: [],
    fetching: false,
  })

  const fetchUser = debounce(async (value) => {
    searchState.data = []
    searchState.fetching = true

    const params: SearchParams = {
      page: 1,
      pageSize: 10,
      username: value,
    }
    try {
      const res = await getUsers(params)
      const data = res.results.map((user) => ({
        label: user.username,
        value: user.id,
      }))
      searchState.data = data
      searchState.fetching = false
    } catch (error) {}
  }, 300)

  watch(formState.segments, () => {
    searchState.data = []
    searchState.fetching = false
  })

  watch(formState, () => {
    disabled.value =
      !formState.content ||
      (formState.audienceType === 'segment' && formState.segments.length === 0)
  })

  const onSubmit = async () => {
    changeOkLoading(true)
    isLoading.value = true
    const segments = formState.segments.map((el) => {
      const segment: SegmentModel = {
        id: el.key,
        username: el.label,
      }
      return segment
    })
    const pushMessage: MessageModel = {
      title: formState.title,
      content: formState.content,
      imageUrl: formState.imageUrl,
      link: formState.link,
      audienceType: formState.audienceType,
      segments: segments,
    }
    try {
      await postMessage(pushMessage)
      isLoading.value = false
      message.success(`Sent Push Successfully`)
      changeOkLoading(false)
      onReset()
      closeModal()
      emit('finished', { source: 'message' })
    } catch (error) {
      isLoading.value = false
    }
  }

  const onReset = async () => {
    formState.audienceType = 'all'
    formState.segment = []
    formState.title = ''
    formState.content = ''
    formState.imageUrl = ''
    formState.link = ''
    fileList.value = []
  }
</script>

<style scoped>
  .avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
  }

  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .preivew-warp {
    width: 450px;
    height: 660px;
    margin: auto;
    background-image: url('https://cdn.meetok.me/2023/12/26/android_preview_777c423868.svg');
    background-repeat: no-repeat;
    background-size: cover;
    overflow: hidden;
  }

  .preview-card {
    position: relative;
    top: 200px;
    margin: auto;
    width: 404px;
    cursor: pointer;
    user-select: none;
  }

  .preview-box {
    background-color: rgb(255, 255, 255);
    box-shadow: rgba(24, 39, 75, 0.12) 0px 6px 12px -6px, rgba(24, 39, 75, 0.08) 0px 8px 24px -4px;
    gap: 12px 16px;
    padding: 20px 16px;
    border-radius: 28px;
  }

  .preview-image {
    width: 100%;
    max-height: 160px;
    object-fit: cover;
    background-color: rgb(255, 255, 255);
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    border-radius: 16px;
  }
</style>
