<template>
  <PageWrapper title="Sent Messages" contentBackground>
    <div class="mb-4 p-4 bg-white">
      <div style="margin-bottom: 16px">
        <a-space wrap>
          <a-button @click="fetchData"> refresh </a-button>
          <a-button @click="goNewPush" type="primary"> New Message </a-button>
        </a-space>
      </div>
      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        :expand-column-width="100"
        size="small"
      >
        <a-table-column key="id" title="Id" data-index="id" :width="50" />
        <a-table-column key="title" title="Title" data-index="title" ellipsis="true">
          <template #customRender="{ record }">
            <a-tooltip placement="topLeft" :title="record.title">
              {{ record.title || '-' }}
            </a-tooltip>
          </template>
        </a-table-column>
        <a-table-column key="content" title="Content" data-index="content">
          <template #customRender="{ record }">
            <a-tooltip placement="topLeft" :title="record.content">
              {{ record.content || '-' }}
            </a-tooltip>
          </template>
        </a-table-column>
        <a-table-column key="imageUrl" title="Image" data-index="imageUrl" :width="80" fixed="left">
          <template #customRender="{ record }">
            <div
              v-if="record.imageUrl"
              class="table-image mx-auto transform text-center hover:scale-110 duration-300 ease-in-out overflow-hidden w-50px h-50px"
            >
              <a-image :width="50" :height="50" :src="record.imageUrl" />
            </div>
            <span v-else>-</span>
          </template>
        </a-table-column>
        <a-table-column key="link" title="Link" data-index="link" ellipsis="true" :width="150">
          <template #customRender="{ record }">
            <a-tooltip placement="topLeft" :title="record.link">
              {{ record.link || '-' }}
            </a-tooltip>
          </template>
        </a-table-column>
        <a-table-column key="audienceType" title="Type" data-index="audienceType" :width="80">
          <template #customRender="{ record }">
            <a-tag>{{ record.audienceType }}</a-tag>
          </template></a-table-column
        >
        <a-table-column key="segments" title="Segments" data-index="segments" ellipsis="true">
          <template #customRender="{ record }">
            <template v-if="record.segments">
              <a-tooltip placement="topLeft" :title="getSegmentText(record.segments)">
                <a-tag color="blue" v-for="segment in record.segments" :key="segment.id">{{
                  segment.username
                }}</a-tag>
              </a-tooltip>
            </template>
            <template v-else>-</template>
          </template>
        </a-table-column>
        <a-table-column key="createdAt" title="Created Time" data-index="createdAt" :width="140">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
        <a-table-column key="status" title="status" data-index="status" :width="100">
          <template #customRender="{ record }">
            <div>
              <a-tag :color="getTagColor(record.status)">
                <template #icon>
                  <check-circle-outlined v-if="record.status == 'sent'" />
                  <sync-outlined :spin="true" v-if="record.status == 'sending'" />
                  <clock-circle-outlined v-if="record.status == 'unsent'" />
                </template>
                {{ record.status }}</a-tag
              >
            </div>
          </template>
        </a-table-column>
      </a-table>
    </div>
    <MessageModal @register="messageModalRegister" @finished="onModalClosed" />
  </PageWrapper>
</template>
<script lang="ts" setup name="usersList">
  import { ref, onActivated } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { getMessages } from '/@/api/messages/messages'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { SearchParams, MessageModel } from '/@/api/messages/model/messageModel'
  import { CheckCircleOutlined, SyncOutlined, ClockCircleOutlined } from '@ant-design/icons-vue'
  import MessageModal from '../messages/components/MessageModal.vue'
  import { useModal } from '/@/components/Modal'

  const [messageModalRegister, { openModal: openMessageModal }] = useModal()

  const isLoading = ref(false)
  const dataSource = ref<MessageModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const onChange = (pageChanged) => {
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const getSegmentText = (segments) => {
    return segments.map((segment) => segment.username).join(' | ')
  }

  const getTagColor = (status) => {
    if (status === 'sending') {
      return 'processing'
    } else if (status === 'sent') {
      return 'success'
    } else {
      return 'default'
    }
  }

  const goNewPush = (): void => {
    openMessageModal(true, {
      audienceType: 'all',
      selectedUsers: [],
    })
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: SearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
    }

    try {
      const res = await getMessages(params)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      isLoading.value = false
    }
  }
  onActivated(() => {
    fetchData()
  })

  const onModalClosed = () => {
    fetchData()
  }
</script>
<style scoped>
  .table-image :deep(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
