export const columns = [
  {
    title: 'Event Title',
    dataIndex: 'title',
    width: 180,
    align: 'left',
  },
  {
    title: 'Event Image',
    dataIndex: 'photos',
    key: 'photos',
    width: 120,
    align: 'center',
  },
  {
    title: 'Star',
    dataIndex: 'star',
    key: 'star',
    width: 120,
    align: 'center',
  },
  {
    title: 'Event Address',
    dataIndex: 'address',
    align: 'left',
  },
  {
    title: 'Event Type',
    dataIndex: 'type',
    key: 'type',
    width: 150,
    align: 'center',
  },
  {
    title: 'Event Time',
    dataIndex: 'startDate',
    width: 150,
    sorter: true,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleString() : ''
    },
  },
  {
    title: 'Max Members',
    dataIndex: 'maxMembers',
    key: 'members',
    width: 120,
    align: 'center',
  },
  {
    title: 'Created At',
    dataIndex: 'createdAt',
    width: 180,
    sorter: true,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleString() : ''
    },
  },
  {
    title: 'Actions',
    key: 'action',
    width: 80,
    fixed: 'right',
  },
]

export const searchFormSchema = [
  {
    field: 'title',
    label: 'Event Title',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'starName',
    label: 'Star Name',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'type',
    label: 'Event Type',
    component: 'Select',
    componentProps: {
      options: [
        { label: 'Normal Event', value: 'normal' },
        { label: 'Special Guest', value: 'specialGuest' },
      ],
    },
    colProps: { span: 8 },
  },
  {
    field: 'timeRange',
    label: 'Event Time Range',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['Start Date', 'End Date'],
      showTime: false,
    },
    colProps: { span: 8 },
  },
]
