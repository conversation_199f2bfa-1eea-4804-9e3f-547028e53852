<template>
  <PageWrapper title="Star Events" content="" class="p-2">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <div class="mb-4">
          <a-button type="primary" @click="handleCreate">
            <Icon icon="ant-design:plus-outlined" />
            Create Event
          </a-button>
        </div>

        <a-table
          :columns="columns"
          :dataSource="dataSource"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          bordered
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <!-- action buttons -->
              <a-space>
                <!-- <a-tooltip title="View Event Details">
              <a-button type="link" @click="handleView(record)">
                <Icon icon="ant-design:eye-outlined" />
              </a-button>
            </a-tooltip> -->
                <a-tooltip title="Edit Event">
                  <a-button type="link" @click="handleEdit(record)">
                    <Icon icon="ant-design:edit-outlined" />
                  </a-button>
                </a-tooltip>
                <a-tooltip v-if="record.type === 'specialGuest'" title="Add to Weekly Schedule">
                  <a-button type="link" @click="handleAddToWeekly(record)">
                    <Icon icon="ant-design:calendar-outlined" />
                  </a-button>
                </a-tooltip>
                <a-popconfirm
                  title="Confirm deletion?"
                  placement="left"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <Icon icon="ant-design:delete-outlined" />
                  </a-button>
                </a-popconfirm>
              </a-space>
              <!-- end of action buttons -->
            </template>

            <template v-if="column.key === 'type'">
              <Badge
                :color="record.type === 'normal' ? 'blue' : 'red'"
                :text="getEventTypeText(record.type)"
              />
            </template>

            <template v-if="column.key === 'members'">
              <a class="members-link" @click="handleViewMembers(record)">{{ record.maxMembers }}</a>
            </template>

            <template v-if="column.key === 'photos'">
              <template v-if="record.photos && record.photos.length > 0">
                <div class="photos-stack">
                  <div
                    v-for="(photo, index) in record.photos.slice(0, 3)"
                    :key="index"
                    class="table-image-container"
                    :style="{ zIndex: record.photos.length - index, right: `${index * 10}px` }"
                  >
                    <template v-if="photo.mime && photo.mime.startsWith('video/')">
                      <Image
                        :src="videoPlaceholder"
                        :width="50"
                        :height="50"
                        :preview="{
                          src: photo.url,
                          visible: false,
                        }"
                        @click="handlePreviewMedia(photo, record.photos)"
                      />
                    </template>
                    <template v-else>
                      <Image
                        :src="photo.url"
                        :width="50"
                        :height="50"
                        :preview="{
                          src: photo.url,
                          visible: false,
                        }"
                        @click="handlePreviewMedia(photo, record.photos)"
                      />
                    </template>
                  </div>
                  <span v-if="record.photos.length > 3" class="more-photos"
                    >+{{ record.photos.length - 3 }}</span
                  >
                </div>
              </template>
              <template v-else>
                <span>No Image</span>
              </template>
            </template>

            <template v-if="column.key === 'star'">
              <span v-if="record.star">
                <a class="star-link" @click="showStarDetail(record.star)">{{
                  record.star.fullname || record.star.username
                }}</a>
              </span>
              <span v-else> - </span>
            </template>

            <template v-if="column.key === 'status'">
              <Badge
                :color="
                  record.status === 'joined'
                    ? 'green'
                    : record.status === 'applied'
                    ? 'blue'
                    : 'red'
                "
                :text="record.status"
              />
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <!-- 对于申请状态的成员，显示批准和拒绝按钮 -->
                <template v-if="record.status === 'applied'">
                  <a-tooltip title="Approve">
                    <a-button type="link" @click="handleApproveRejectMember(record, 'joined')">
                      <Icon icon="ant-design:check-outlined" />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip title="Reject">
                    <a-button
                      type="link"
                      danger
                      @click="handleApproveRejectMember(record, 'rejected')"
                    >
                      <Icon icon="ant-design:close-outlined" />
                    </a-button>
                  </a-tooltip>
                </template>

                <!-- 对于已加入状态的成员，显示拒绝按钮 -->
                <template v-else-if="record.status === 'joined'">
                  <a-tooltip title="Reject">
                    <a-button
                      type="link"
                      danger
                      @click="handleApproveRejectMember(record, 'rejected')"
                    >
                      <Icon icon="ant-design:close-outlined" />
                    </a-button>
                  </a-tooltip>
                </template>

                <!-- 对于已拒绝状态的成员，显示批准按钮 -->
                <template v-else-if="record.status === 'rejected'">
                  <a-tooltip title="Approve">
                    <a-button type="link" @click="handleApproveRejectMember(record, 'joined')">
                      <Icon icon="ant-design:check-outlined" />
                    </a-button>
                  </a-tooltip>
                </template>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 媒体预览 Modal -->
    <Modal
      v-model:visible="previewVisible"
      :footer="null"
      :width="800"
      centered
      @cancel="closePreview"
      class="media-preview-modal"
    >
      <div class="media-preview-container">
        <div class="media-navigation">
          <a-button class="nav-button prev" :disabled="currentIndex === 0" @click="prevMedia">
            <Icon icon="ant-design:left-outlined" />
          </a-button>
          <a-button
            class="nav-button next"
            :disabled="currentIndex === mediaList.length - 1"
            @click="nextMedia"
          >
            <Icon icon="ant-design:right-outlined" />
          </a-button>
        </div>

        <div class="media-content">
          <template v-if="currentMedia?.mime && currentMedia.mime.startsWith('video/')">
            <video
              controls
              autoplay
              class="preview-video"
              :src="currentMedia.url"
              :poster="
                currentMedia.coverUrl ||
                currentMedia.thumbnailUrl ||
                getVideoPoster(currentMedia.url)
              "
              @ended="videoEnded"
            ></video>
          </template>
          <template v-else>
            <img class="preview-image" :src="currentMedia?.url" alt="Preview Image" />
          </template>
        </div>

        <div class="media-counter"> {{ currentIndex + 1 }} / {{ mediaList.length }} </div>
      </div>
    </Modal>

    <!-- 添加到周报的弹窗 -->
    <a-modal
      v-model:visible="weeklyModalVisible"
      title="Add to Weekly Schedule"
      @ok="handleWeeklyModalOk"
      @cancel="closeWeeklyModal"
      :confirmLoading="weeklyModalLoading"
      width="500px"
    >
      <div class="p-6">
        <a-form
          :model="weeklyFormState"
          :rules="weeklyFormRules"
          ref="weeklyFormRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-item label="Playing Date" name="playingDate">
            <a-date-picker
              v-model:value="weeklyFormState.playingDate"
              show-time
              format="YYYY-MM-DD HH:mm"
              style="width: 100%"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 成员列表弹窗 -->
    <a-modal
      v-model:visible="membersModalVisible"
      :title="`Members - ${currentEvent?.title || ''}`"
      width="700px"
      @cancel="closeMembersModal"
      :footer="null"
    >
      <div class="p-6">
        <a-table
          :columns="memberColumns"
          :dataSource="eventMembers"
          :loading="membersLoading"
          :pagination="membersPagination"
          @change="handleMembersTableChange"
          bordered
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avatar'">
              <a-avatar :src="record.user.avatar?.url" :size="40" shape="square">
                {{ record.user.username ? record.user.username.charAt(0).toUpperCase() : 'U' }}
              </a-avatar>
            </template>
            <template v-if="column.key === 'status'">
              <Badge
                :color="
                  record.status === 'joined'
                    ? 'green'
                    : record.status === 'applied'
                    ? 'blue'
                    : 'red'
                "
                :text="record.status"
              />
            </template>
            <template v-if="column.key === 'action'">
              <a-space>
                <a-tooltip v-if="record.status === 'applied'" title="Approve">
                  <a-button type="link" @click="handleApproveRejectMember(record, 'joined')">
                    <Icon icon="ant-design:check-outlined" />
                  </a-button>
                </a-tooltip>
                <a-tooltip v-if="record.status === 'applied'" title="Reject">
                  <a-button
                    type="link"
                    danger
                    @click="handleApproveRejectMember(record, 'rejected')"
                  >
                    <Icon icon="ant-design:close-outlined" />
                  </a-button>
                </a-tooltip>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { ref, onMounted, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { Icon } from '/@/components/Icon'
  import { Badge, Image, Modal, FormInstance } from 'ant-design-vue'
  import { useMessage } from '/@/hooks/web/useMessage'
  import {
    getStarEvents,
    deleteStarEvent,
    getStarEventMembers,
    approveJoinRequest,
    rejectJoinRequest,
  } from '/@/api/star-event/star-event'
  import { columns } from './starEvent.data'
  import { StarEventModel } from '/@/api/star-event/model/starEventModels'
  import { PageWrapper } from '/@/components/Page'
  import videoPlaceholder from '/@/assets/images/video-placeholder.png'
  import { useGo } from '/@/hooks/web/usePage'
  import { createStarSchedule } from '/@/api/star-event/star-schedule'

  const router = useRouter()
  const { createMessage } = useMessage()

  // Table data and state
  const loading = ref(false)
  const dataSource = ref<StarEventModel[]>([])
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `Total: ${total} items`,
  })

  // 媒体预览状态
  const previewVisible = ref(false)
  const currentMedia = ref<any>(null)
  const mediaList = ref<any[]>([])
  const currentIndex = ref(0)

  // 周报添加弹窗状态
  const weeklyModalVisible = ref(false)
  const weeklyModalLoading = ref(false)
  const weeklyFormRef = ref<FormInstance | null>(null)
  const currentEvent = ref<StarEventModel | null>(null)

  // 周报表单状态
  const weeklyFormState = reactive({
    playingDate: undefined as any,
  })

  // 周报表单验证规则
  const weeklyFormRules = {
    playingDate: [{ required: true, message: 'Please select a playing date', trigger: 'change' }],
  }

  // 成员列表状态
  const membersModalVisible = ref(false)
  const membersLoading = ref(false)
  const eventMembers = ref<any[]>([])
  const membersPagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total) => `Total: ${total} items`,
  })

  // 成员列表列定义
  const memberColumns = [
    {
      title: 'Avatar',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 80,
      align: 'center',
    },
    {
      title: 'Full Name',
      dataIndex: ['user', 'fullname'],
      key: 'fullname',
      width: 150,
      customRender: ({ record }) => {
        return record.user.fullname || record.user.username || '-'
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center',
    },
    {
      title: 'Join Time',
      dataIndex: 'createdAt',
      width: 150,
      customRender: ({ text }) => {
        return text ? new Date(text).toLocaleString() : ''
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: 120,
      align: 'center',
    },
  ]

  // 处理媒体预览
  function handlePreviewMedia(media, allMedia) {
    currentMedia.value = media
    mediaList.value = allMedia
    currentIndex.value = allMedia.findIndex((item) => item.id === media.id)
    previewVisible.value = true
  }

  // 视频播放结束
  function videoEnded() {
    // 可选：自动播放下一个媒体
    if (currentIndex.value < mediaList.value.length - 1) {
      nextMedia()
    }
  }

  // 切换到下一个媒体
  function nextMedia() {
    if (currentIndex.value < mediaList.value.length - 1) {
      currentIndex.value++
      currentMedia.value = mediaList.value[currentIndex.value]
      handleMediaChange()
    }
  }

  // 切换到上一个媒体
  function prevMedia() {
    if (currentIndex.value > 0) {
      currentIndex.value--
      currentMedia.value = mediaList.value[currentIndex.value]
      handleMediaChange()
    }
  }

  // 关闭预览
  function closePreview() {
    previewVisible.value = false
    currentMedia.value = null
    // 如果有视频播放，确保关闭时停止播放
    const videoElement = document.querySelector('.preview-video') as HTMLVideoElement
    if (videoElement) {
      videoElement.pause()
    }
  }

  // 处理视频封面
  function getVideoPoster(_videoUrl: string) {
    // 如果没有封面，返回一个默认的视频封面图
    return '/src/assets/images/video-placeholder.png'
  }

  // 处理媒体切换
  function handleMediaChange() {
    // 当媒体变化时，如果是视频，需要确保旧视频暂停，新视频加载
    setTimeout(() => {
      const videoElement = document.querySelector('.preview-video') as HTMLVideoElement
      if (
        videoElement &&
        currentMedia.value?.mime &&
        currentMedia.value.mime.startsWith('video/')
      ) {
        videoElement.load()
        videoElement.play().catch((err) => {
          console.warn('Auto-play failed:', err)
        })
      }
    }, 100)
  }

  // Load data
  const loadData = async () => {
    loading.value = true
    try {
      const params = {
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
      }
      const res = await getStarEvents(params)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
    } catch (error) {
      console.error('Failed to load data', error)
      createMessage.error('Failed to load data')
    } finally {
      loading.value = false
    }
  }

  // Handle table change
  const handleTableChange = (pag) => {
    pagination.value.current = pag.current
    pagination.value.pageSize = pag.pageSize
    loadData()
  }

  // Get event type text
  function getEventTypeText(type: string) {
    switch (type) {
      case 'normal':
        return 'Normal Event'
      case 'specialGuest':
        return 'Special Guest'
      default:
        return type
    }
  }

  // Create event
  function handleCreate() {
    router.push('/star-event/create')
  }

  // View event details
  /* function handleView(record) {
    router.push(`/star-event/detail/${record.id}`)
  } */

  // Edit event
  function handleEdit(record) {
    router.push(`/star-event/edit/${record.id}`)
  }

  // Delete event
  async function handleDelete(record) {
    try {
      await deleteStarEvent(record.id)
      createMessage.success('Deleted successfully')
      loadData() // Reload data
    } catch (error) {
      console.error('Delete failed', error)
      createMessage.error('Delete failed')
    }
  }

  const go = useGo()

  // Show star detail
  function showStarDetail(record) {
    go({
      name: 'user',
      params: { id: record.id, username: record.username },
    })
  }

  // 处理添加到周报
  function handleAddToWeekly(record: StarEventModel) {
    currentEvent.value = record
    weeklyFormState.playingDate = undefined
    weeklyFormRef.value?.resetFields()
    weeklyModalVisible.value = true
  }

  // 处理周报弹窗确认
  async function handleWeeklyModalOk() {
    try {
      // 表单验证
      await weeklyFormRef.value?.validate()

      weeklyModalLoading.value = true

      const formData = {
        eventId: currentEvent.value?.id,
        playingDate: weeklyFormState.playingDate
          ? weeklyFormState.playingDate.format('YYYY-MM-DD HH:mm:ss')
          : undefined,
      }

      // 创建周报
      await createStarSchedule(formData)
      createMessage.success('Added to weekly schedule successfully')

      // 关闭弹窗
      closeWeeklyModal()
    } catch (error) {
      console.error('Form validation failed or API error', error)
      createMessage.error('Failed to add to weekly schedule')
    } finally {
      weeklyModalLoading.value = false
    }
  }

  // 关闭周报弹窗
  function closeWeeklyModal() {
    weeklyModalVisible.value = false
    currentEvent.value = null
    weeklyFormState.playingDate = undefined
    weeklyFormRef.value?.resetFields()
  }

  // 查看成员列表
  function handleViewMembers(record: StarEventModel) {
    currentEvent.value = record
    membersPagination.value.current = 1
    loadEventMembers()
    membersModalVisible.value = true
  }

  // 加载事件成员
  const loadEventMembers = async () => {
    if (!currentEvent.value?.id) return

    membersLoading.value = true
    try {
      const params = {
        page: membersPagination.value.current,
        pageSize: membersPagination.value.pageSize,
      }
      const res = await getStarEventMembers(currentEvent.value.id, params)
      eventMembers.value = res.results
      membersPagination.value.total = res.pagination.total
    } catch (error) {
      console.error('Failed to load event members', error)
      createMessage.error('Failed to load event members')
    } finally {
      membersLoading.value = false
    }
  }

  // 处理成员表格分页变化
  const handleMembersTableChange = (pag) => {
    membersPagination.value.current = pag.current
    membersPagination.value.pageSize = pag.pageSize
    loadEventMembers()
  }

  // 关闭成员弹窗
  function closeMembersModal() {
    membersModalVisible.value = false
    currentEvent.value = null
    eventMembers.value = []
  }

  // 处理成员审批/拒绝
  async function handleApproveRejectMember(record, newStatus: 'joined' | 'rejected') {
    try {
      if (!currentEvent.value?.id) return

      membersLoading.value = true

      // 根据目标状态调用不同的API
      if (newStatus === 'joined') {
        await approveJoinRequest(currentEvent.value.id, { memberId: record.id })
        createMessage.success('Member approved successfully')
      } else {
        await rejectJoinRequest(currentEvent.value.id, { memberId: record.id })
        createMessage.success('Member rejected successfully')
      }

      // 更新本地数据状态，避免刷新整个列表
      const index = eventMembers.value.findIndex((m) => m.id === record.id)
      if (index !== -1) {
        eventMembers.value[index].status = newStatus
      }
    } catch (error) {
      console.error(`Failed to ${newStatus} member`, error)
      createMessage.error(`Failed to ${newStatus === 'joined' ? 'approve' : 'reject'} member`)
    } finally {
      membersLoading.value = false
    }
  }

  // Initial load
  onMounted(() => {
    loadData()
  })
</script>

<style scoped>
  .table-image-container {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    overflow: hidden;
    position: relative;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
    cursor: pointer;
  }

  .table-image-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .table-image-container :deep(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .photos-stack {
    position: relative;
    height: 50px;
    display: flex;
    justify-content: flex-start;
    padding-right: 40px;
  }

  .more-photos {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 2px 5px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1;
  }

  /* 视频容器样式 */
  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .video-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    z-index: 2;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .video-thumbnail {
    opacity: 0.9;
    filter: brightness(0.9);
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* 媒体预览样式 */
  :deep(.media-preview-modal) {
    .ant-modal-content {
      background-color: rgba(0, 0, 0, 0.85);

      .ant-modal-close {
        color: #fff;
      }

      .ant-modal-body {
        padding: 0;
      }
    }
  }

  .media-preview-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
  }

  .media-navigation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    pointer-events: none;
  }

  .nav-button {
    background-color: rgba(255, 255, 255, 0.3);
    border: none;
    pointer-events: auto;
    z-index: 10;

    &:hover {
      background-color: rgba(255, 255, 255, 0.5);
    }

    &[disabled] {
      background-color: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .media-content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    max-height: 80vh;
  }

  .preview-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
  }

  .preview-video {
    max-width: 100%;
    max-height: 80vh;
  }

  .media-counter {
    position: absolute;
    bottom: 10px;
    right: 10px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .star-link {
    color: #1890ff;
    cursor: pointer;
  }

  .star-link:hover {
    text-decoration: underline;
  }

  .members-link {
    color: #1890ff;
    cursor: pointer;
    font-weight: bold;
  }

  .members-link:hover {
    text-decoration: underline;
  }
</style>
