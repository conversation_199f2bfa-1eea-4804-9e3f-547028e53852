export const columns = [
  {
    title: 'Playing Date',
    dataIndex: 'playingDate',
    key: 'playingDate',
    width: 160,
    sorter: true,
  },
  {
    title: 'Event Name',
    dataIndex: ['event', 'title'],
    key: 'eventName',
    width: 180,
    customRender: ({ record }) => {
      return record.event?.title || 'No Event'
    },
  },
  {
    title: 'Preview Image',
    key: 'previewImage',
    width: 120,
    align: 'center',
  },
  {
    title: 'Address',
    dataIndex: ['event', 'address'],
    key: 'address',
    width: 200,
    ellipsis: true,
    customRender: ({ record }) => {
      return record.event?.address || '-'
    },
  },
  {
    title: 'Type',
    dataIndex: ['event', 'type'],
    key: 'type',
    width: 100,
    customRender: ({ record }) => {
      const type = record.event?.type || '-'
      return type
    },
  },
  {
    title: 'Created At',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 160,
    sorter: true,
  },
  {
    title: 'Actions',
    key: 'action',
    width: 120,
    fixed: 'right',
  },
]

export const searchFormSchema = [
  {
    field: 'eventId',
    label: 'Event',
    component: 'Select',
    componentProps: {
      placeholder: 'Select Event',
      options: [],
      allowClear: true,
    },
    colProps: { span: 8 },
  },
  {
    field: 'dateRange',
    label: 'Playing Date Range',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['Start Date', 'End Date'],
      showTime: false,
    },
    colProps: { span: 8 },
  },
]
