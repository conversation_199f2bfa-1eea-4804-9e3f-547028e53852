<template>
  <PageWrapper title="Weekly Star check" content="" class="p-2">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <!-- Search Form - 根据需要可以添加搜索表单 -->
        <div class="mb-4">
          <a-row :gutter="16" align="middle">
            <a-col>
              <span>Date Range:</span>
            </a-col>
            <a-col>
              <a-range-picker
                v-model:value="searchParams.dateRange"
                format="YYYY-MM-DD"
                :placeholder="['Start Date', 'End Date']"
                style="width: 300px"
              />
            </a-col>
            <a-col>
              <a-space>
                <a-button type="primary" @click="handleSearch" :loading="loading">Search</a-button>
                <a-button @click="resetSearch" :loading="loading">Reset</a-button>
              </a-space>
            </a-col>
          </a-row>
        </div>

        <!-- Create Button -->
        <div class="mb-4">
          <a-button type="primary" @click="handleCreate">
            <Icon icon="ant-design:plus-outlined" />
            Add
          </a-button>
        </div>

        <!-- Schedule Table -->
        <a-table
          :columns="columns"
          :dataSource="dataSource"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          bordered
          size="small"
          rowKey="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a-tooltip title="Edit Schedule">
                  <a-button type="link" @click="handleEdit(record)">
                    <Icon icon="ant-design:edit-outlined" />
                  </a-button>
                </a-tooltip>
                <a-popconfirm
                  title="Are you sure you want to delete this schedule?"
                  placement="left"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <Icon icon="ant-design:delete-outlined" />
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
            <template v-if="column.key === 'eventName'">
              <div class="event-name-cell">
                <span>{{ record.event?.title || 'No Event' }}</span>
                <a-tag v-if="isCurrentSchedule(record)" color="green" class="ml-2"> Current </a-tag>
              </div>
            </template>
            <template v-if="column.key === 'previewImage'">
              <div class="preview-image-cell">
                <a-image
                  v-if="record.previewImage?.url"
                  :src="record.previewImage.url"
                  :width="50"
                  :height="50"
                  :preview="true"
                  class="schedule-preview-image"
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
                <span v-else class="no-image-text">No Image</span>
              </div>
            </template>
            <template v-if="column.key === 'playingDate'">
              <div>{{ formatDateTime(record.playingDate) }}</div>
            </template>
            <template v-if="column.key === 'createdAt'">
              <div>{{ formatDateTime(record.createdAt) }}</div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 日程表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="closeModal"
      :confirmLoading="modalLoading"
      width="500px"
    >
      <div class="p-6">
        <a-form
          :model="formState"
          :rules="formRules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-item label="Event" name="eventId">
            <a-select
              v-model:value="formState.eventId"
              placeholder="Select an event"
              :options="eventOptions"
              show-search
              :filter-option="filterOption"
            />
          </a-form-item>
          <a-form-item label="Playing Date" name="playingDate">
            <a-date-picker
              v-model:value="formState.playingDate"
              show-time
              format="YYYY-MM-DD HH:mm"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="Preview Image" name="previewImage">
            <a-upload
              v-model:file-list="previewFileList"
              accept="image/*"
              name="files"
              :headers="headers"
              list-type="picture-card"
              :action="uploadUrl"
              :before-upload="beforeUpload"
              @change="handlePreviewUploadChange"
              @preview="handlePreview"
              :max-count="1"
            >
              <div v-if="!previewFileList || previewFileList.length < 1">
                <PlusOutlined />
                <div style="margin-top: 8px">Upload</div>
              </div>
            </a-upload>
            <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
              <img alt="preview" style="width: 100%" :src="previewImage" />
            </a-modal>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { onMounted, ref, reactive, computed } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { useMessage } from '/@/hooks/web/useMessage'
  import { Icon } from '/@/components/Icon'
  import { columns } from './starSchedule.data'
  import {
    getStarSchedules,
    deleteStarSchedule,
    StarScheduleModel,
    createStarSchedule,
    updateStarSchedule,
    getStarSchedule,
  } from '/@/api/star-event/star-schedule'
  import { getStarEvents } from '/@/api/star-event/star-event'
  import { FormInstance } from 'ant-design-vue'
  import { dateUtil } from '/@/utils/dateUtil'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { useGlobSetting } from '/@/hooks/setting'
  import { getToken } from '/@/utils/auth'
  import type { UploadProps, UploadChangeParam } from 'ant-design-vue'
  import { message } from 'ant-design-vue'

  const { createMessage } = useMessage()

  // 图片上传相关
  const previewFileList = ref<UploadProps['fileList']>([])
  const previewVisible = ref(false)
  const previewImage = ref('')
  const uploadUrl = useGlobSetting().apiUrl + '/upload'
  const headers = {
    Authorization: `Bearer ${getToken()}`,
  }

  // 表格数据和状态
  const loading = ref(false)
  const dataSource = ref<StarScheduleModel[]>([])
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total: number) => `Total: ${total} items`,
  })

  // 事件选项
  const eventOptions = ref<{ label: string; value: number }[]>([])

  // 格式化日期时间
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '-'
    return dateUtil(dateString).format('YYYY-MM-DD HH:mm')
  }

  // 搜索参数
  const searchParams = reactive({
    dateRange: undefined as [any, any] | undefined,
  })

  // 弹窗状态
  const modalVisible = ref(false)
  const modalLoading = ref(false)
  const modalType = ref<'create' | 'edit'>('create')
  const currentId = ref<number | null>(null)
  const formRef = ref<FormInstance | null>(null)

  // 表单状态
  const formState = reactive({
    eventId: undefined as number | undefined,
    playingDate: undefined as any,
    previewImage: undefined as any,
  })

  // 表单验证规则
  const formRules = {
    eventId: [{ required: true, message: 'Please select an event', trigger: 'change' }],
    playingDate: [{ required: true, message: 'Please select a playing date', trigger: 'change' }],
  }

  // 计算弹窗标题
  const modalTitle = computed(() => {
    return modalType.value === 'create' ? 'Add' : 'Edit'
  })

  // 过滤选项方法
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  // 判断是否是当前日程的方法
  const isCurrentSchedule = (record: StarScheduleModel) => {
    if (!record.playingDate) return false

    const now = new Date()
    const playingDate = new Date(record.playingDate)

    // 如果播放日期还未到达，则不是current
    if (playingDate > now) return false

    // 查找所有已到达时间的日程，按播放日期降序排列
    const pastSchedules = dataSource.value
      .filter((item) => {
        if (!item.playingDate) return false
        return new Date(item.playingDate) <= now
      })
      .sort((a, b) => new Date(b.playingDate).getTime() - new Date(a.playingDate).getTime())

    // 如果当前记录是最近的一个已到达时间的日程，则为current
    return pastSchedules.length > 0 && pastSchedules[0].id === record.id
  }

  // 加载数据
  const loadData = async () => {
    loading.value = true
    try {
      const params: any = {
        page: pagination.value.current,
        pageSize: pagination.value.pageSize,
      }

      // 处理日期范围
      if (
        searchParams.dateRange &&
        searchParams.dateRange.length === 2 &&
        searchParams.dateRange[0] &&
        searchParams.dateRange[1]
      ) {
        params.playingDateFrom = searchParams.dateRange[0].format('YYYY-MM-DD')
        params.playingDateTo = searchParams.dateRange[1].format('YYYY-MM-DD')
      }

      const res = await getStarSchedules(params)
      // 确保数据结构正确
      if (res && res.results) {
        dataSource.value = res.results
        if (res.pagination) {
          pagination.value.total = res.pagination.total
        }
      }
    } catch (error) {
      console.error('Failed to load data', error)
      createMessage.error('Failed to load data')
    } finally {
      loading.value = false
    }
  }

  // 加载事件选项
  const loadEventOptions = async () => {
    try {
      const res = await getStarEvents({ pageSize: 100 })
      if (res && res.results) {
        eventOptions.value = res.results.map((item) => ({
          label: item.title || '未命名事件',
          value: item.id,
        }))
      }
    } catch (error) {
      console.error('Failed to load event options', error)
      createMessage.error('Failed to load event options')
    }
  }

  // 处理表格变化
  const handleTableChange = (pag: any) => {
    pagination.value.current = pag.current
    pagination.value.pageSize = pag.pageSize
    loadData()
  }

  // 处理搜索
  const handleSearch = async () => {
    try {
      console.log('Search button clicked', searchParams.dateRange)

      // 验证日期范围
      if (searchParams.dateRange && searchParams.dateRange.length === 2) {
        const [startDate, endDate] = searchParams.dateRange
        if (startDate && endDate && startDate.valueOf() > endDate.valueOf()) {
          createMessage.warning('Start date cannot be later than end date')
          return
        }
      }

      pagination.value.current = 1 // 重置到第一页
      await loadData()
    } catch (error) {
      console.error('Search failed', error)
      createMessage.error('Search failed')
    }
  }

  // 重置搜索
  const resetSearch = async () => {
    try {
      searchParams.dateRange = undefined
      pagination.value.current = 1
      await loadData()
    } catch (error) {
      console.error('Reset search failed', error)
      createMessage.error('Reset search failed')
    }
  }

  // 重置表单
  const resetForm = () => {
    formState.eventId = undefined
    formState.playingDate = undefined
    formState.previewImage = undefined
    previewFileList.value = []
    formRef.value?.resetFields()
  }

  // 创建日程
  function handleCreate() {
    modalType.value = 'create'
    currentId.value = null
    resetForm()
    modalVisible.value = true
  }

  // 编辑日程
  async function handleEdit(record: StarScheduleModel) {
    try {
      modalType.value = 'edit'
      currentId.value = record.id
      modalVisible.value = true

      // 获取最新的日程数据
      const detailData = await getStarSchedule(record.id)

      // 设置表单数据
      formState.eventId = detailData.event?.id
      formState.playingDate = detailData.playingDate ? dateUtil(detailData.playingDate) : undefined

      // 设置预览图片
      if (detailData.previewImage) {
        formState.previewImage = detailData.previewImage
        previewFileList.value = [
          {
            uid: String(detailData.previewImage.id),
            name: `preview-${detailData.previewImage.id}`,
            status: 'done',
            url: detailData.previewImage.url,
            thumbUrl: detailData.previewImage.url,
          },
        ]
      } else {
        formState.previewImage = undefined
        previewFileList.value = []
      }
    } catch (error) {
      console.error('Failed to get schedule detail', error)
      createMessage.error('Failed to get schedule detail')
    }
  }

  // 删除日程
  async function handleDelete(record: StarScheduleModel) {
    try {
      await deleteStarSchedule(record.id)
      createMessage.success('Schedule deleted successfully')
      loadData() // 重新加载数据
    } catch (error) {
      console.error('Failed to delete schedule', error)
      createMessage.error('Failed to delete schedule')
    }
  }

  // 处理弹窗确认
  async function handleModalOk() {
    try {
      // 表单验证
      await formRef.value?.validate()

      modalLoading.value = true

      const formData = {
        eventId: formState.eventId,
        playingDate: formState.playingDate
          ? formState.playingDate.format('YYYY-MM-DD HH:mm')
          : undefined,
        previewImage: formState.previewImage
          ? {
              id: formState.previewImage.id,
              url: formState.previewImage.url,
              formats: formState.previewImage.formats || {},
            }
          : undefined,
      }

      if (modalType.value === 'create') {
        // 创建日程
        await createStarSchedule(formData)
        createMessage.success('Schedule created successfully')
      } else {
        // 更新日程
        if (currentId.value) {
          await updateStarSchedule(currentId.value, formData)
          createMessage.success('Schedule updated successfully')
        }
      }

      // 关闭弹窗并重新加载数据
      closeModal()
      loadData()
    } catch (error) {
      console.error('Form validation failed or API error', error)
      createMessage.error('Failed to save schedule')
    } finally {
      modalLoading.value = false
    }
  }

  // 关闭弹窗
  function closeModal() {
    modalVisible.value = false
    resetForm()
  }

  // 图片上传相关函数
  function getBase64(file: File) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview && file.originFileObj) {
      file.preview = (await getBase64(file.originFileObj as File)) as string
    }
    previewImage.value = file.url || file.preview || ''
    previewVisible.value = true
  }

  const handleCancel = () => {
    previewVisible.value = false
  }

  // 文件上传验证
  function beforeUpload(file: File) {
    const isValidType = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isValidType) {
      message.error('You can only upload image files!')
    }
    if (!isLt2M) {
      message.error('File size must be less than 2MB!')
    }

    return isValidType && isLt2M
  }

  // 处理预览图片上传变化
  function handlePreviewUploadChange(info: UploadChangeParam) {
    previewFileList.value = info.fileList

    if (info.file.status === 'done') {
      // 服务器返回的数据结构
      const response = info.file.response[0]
      // 获取文件ID
      const id = response.id
      // 获取原始文件的uid
      const uid = info.file.originFileObj?.uid
      // 查找当前文件
      const currentFile = previewFileList.value.find((file) => file.uid === uid)

      if (currentFile) {
        // 更新文件的uid为服务器返回的id
        currentFile.uid = String(id)
        // 设置文件url为服务器返回的url
        currentFile.url = response.url
        // 添加预览url
        currentFile.thumbUrl = response.url
        // 添加预览属性
        currentFile.preview = response.url
      }

      message.success(`${info.file.name} uploaded successfully`)

      // 更新表单数据
      formState.previewImage = {
        id: Number(id),
        url: response.url,
        formats: response.formats || {},
      }
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} upload failed`)
    } else if (info.file.status === 'removed') {
      // 文件被移除时清空previewImage
      formState.previewImage = undefined
    }
  }

  // 初始加载
  onMounted(() => {
    loadEventOptions()
    loadData()
  })
</script>

<style scoped>
  .ant-table-tbody > tr > td {
    vertical-align: top;
  }

  .event-name-cell {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .preview-image-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
  }

  .schedule-preview-image {
    border-radius: 4px;
    object-fit: cover;
    border: 1px solid #f0f0f0;
  }

  .no-image-text {
    color: #999;
    font-size: 12px;
    font-style: italic;
  }

  .ml-2 {
    margin-left: 8px;
  }
</style>
