<template>
  <PageWrapper :title="getTitle" @back="goBack">
    <div class="form-container">
      <a-card class="form-card">
        <div class="form-layout">
          <!-- 左侧基本信息表单 -->
          <div class="form-left">
            <a-form
              :model="formState"
              :rules="rules"
              ref="formRef"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              layout="horizontal"
            >
              <!-- Event Title -->
              <a-form-item name="title" label="Title" required>
                <a-input v-model:value="formState.title" placeholder="Enter event title" />
              </a-form-item>

              <!-- Event Type -->
              <a-form-item name="type" label="Type" required>
                <a-select
                  v-model:value="formState.type"
                  placeholder="Select event type"
                  style="width: 100%"
                  @change="handleTypeChange"
                >
                  <a-select-option value="normal">Normal</a-select-option>
                  <a-select-option value="specialGuest">Special Guest</a-select-option>
                </a-select>
              </a-form-item>

              <!-- User Selection (only for specialGuest type) -->
              <a-form-item
                v-if="formState.type === 'specialGuest'"
                name="userId"
                label="Star"
                required
              >
                <a-select
                  v-model:value="formState.userId"
                  placeholder="Select star user"
                  style="width: 100%"
                  :loading="usersLoading"
                  @change="handleUserChange"
                  show-search
                  :filter-option="filterUserOption"
                  @search="handleUserSearch"
                >
                  <a-select-option v-for="user in usersList" :key="user.id" :value="user.id">
                    {{ user.username || user.fullname }}
                  </a-select-option>
                </a-select>
                <!-- User Info Display -->
                <div v-if="selectedUser" class="user-info-card mt-2">
                  <div class="user-info-item">
                    <div class="user-info-label">Name:</div>
                    <div class="user-info-value">{{
                      selectedUser.fullname || selectedUser.username || 'N/A'
                    }}</div>
                  </div>
                  <div class="user-info-item">
                    <div class="user-info-label">Title:</div>
                    <div class="user-info-value">{{ selectedUser.title || 'N/A' }}</div>
                  </div>
                  <div class="user-info-item">
                    <div class="user-info-label">Level:</div>
                    <div class="user-info-value level-stars">
                      <a-rate
                        :value="selectedUser.level || 0"
                        :count="7"
                        disabled
                        :tooltips="levelTooltips"
                      />
                    </div>
                  </div>
                </div>
              </a-form-item>

              <!-- Start Date -->
              <a-form-item name="startDate" label="Date" required>
                <a-date-picker
                  v-model:value="formState.startDate"
                  show-time
                  format="YYYY-MM-DD HH:mm"
                  style="width: 100%"
                  placeholder="Select date and time"
                />
              </a-form-item>

              <!-- Max Members -->
              <a-form-item name="maxMembers" label="Max Attendees" required>
                <a-input-number
                  v-model:value="formState.maxMembers"
                  style="width: 100%"
                  :min="1"
                  :precision="0"
                  placeholder="Enter maximum number of attendees"
                />
              </a-form-item>

              <!-- Event Description -->
              <a-form-item name="description" label="Description">
                <a-textarea
                  v-model:value="formState.description"
                  placeholder="Enter event description"
                  :rows="4"
                />
              </a-form-item>

              <!-- Address -->
              <a-form-item name="address" label="Address" required>
                <a-input
                  v-model:value="formState.address"
                  placeholder="Address will be displayed here"
                />
                <a-form-item-rest>
                  <div class="location-container mt-4">
                    <!-- Map Container with Search Controls overlay -->
                    <div class="map-wrapper">
                      <div class="map-container" ref="mapContainer"></div>

                      <!-- Search and Location Controls Overlay -->
                      <div class="map-controls-overlay">
                        <div class="search-box-container">
                          <a-input
                            type="text"
                            ref="searchInput"
                            placeholder="Search for a location"
                            class="location-search-input"
                            allow-clear
                          />
                        </div>
                        <a-button
                          class="my-location-button"
                          @click="handleMyLocationClick"
                          type="primary"
                          size="normal"
                          :loading="isLocating"
                        >
                          <Icon icon="ion:location-outline" />
                        </a-button>
                      </div>

                      <!-- Coordinates Display -->
                      <div class="coordinates-display">
                        <span>Longitude: {{ formState.longitude.toFixed(6) }}</span>
                        <span class="ml-4">Latitude: {{ formState.latitude.toFixed(6) }}</span>
                      </div>
                    </div>
                  </div>
                </a-form-item-rest>
              </a-form-item>

              <!-- Event Photos -->
              <a-form-item name="photos" label="Photos">
                <a-upload
                  v-model:file-list="fileList"
                  accept="image/*,video/*"
                  name="files"
                  :headers="headers"
                  list-type="picture-card"
                  :action="uploadUrl"
                  :before-upload="beforeUpload"
                  @change="handleUploadChange"
                  @preview="handlePreview"
                >
                  <div v-if="fileList && fileList.length < 5">
                    <plus-outlined />
                    <div style="margin-top: 8px">Upload</div>
                  </div>
                </a-upload>
                <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                  <img alt="example" style="width: 100%" :src="previewImage" />
                </a-modal>
              </a-form-item>
            </a-form>
          </div>

          <!-- 右侧Vditor编辑器 -->
          <div class="form-right">
            <a-form-item
              name="content"
              label="Content"
              class="content-form-item"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
            >
              <div id="vditor" class="vditor-container"></div>
            </a-form-item>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <a-space>
            <a-button @click="goBack">Cancel</a-button>
            <a-button type="primary" @click="submitForm" :loading="submitLoading">Submit</a-button>
          </a-space>
        </div>
      </a-card>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { PageWrapper } from '/@/components/Page'
  import { getStarEvent, createStarEvent, updateStarEvent } from '/@/api/star-event/star-event'
  import { getUsers } from '/@/api/users/users'
  import { useMessage } from '/@/hooks/web/useMessage'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { Icon } from '/@/components/Icon'
  import { message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import { useGlobSetting } from '/@/hooks/setting'
  import { getToken } from '/@/utils/auth'
  import type { UploadProps, UploadChangeParam } from 'ant-design-vue'
  import type { UserModel } from '/@/api/users/model/userModel'
  import type { SelectProps } from 'ant-design-vue/es/select'
  import Vditor from 'vditor'
  import 'vditor/dist/index.css'

  // 添加Google Maps API类型声明
  declare global {
    interface Window {
      google: {
        maps: {
          Map: any
          Marker: any
          InfoWindow: any
          Geocoder: any
          LatLng: any
          Animation: any
          ControlPosition: any
          places: {
            Autocomplete: any
          }
          event: {
            clearInstanceListeners: (instance: any) => void
          }
        }
      }
      initGoogleMaps?: () => void
    }
  }

  const route = useRoute()
  const router = useRouter()
  const { createMessage } = useMessage()
  const formRef = ref<any>(null)
  const mapContainer = ref(null)
  const searchInput = ref(null)
  const isEdit = computed(() => !!route.params.id)
  const getTitle = computed(() => (isEdit.value ? 'Edit Event' : 'Create Event'))
  const submitLoading = ref(false)
  const fileList = ref<UploadProps['fileList']>([])
  const previewVisible = ref(false)
  const previewImage = ref('')
  const uploadUrl = useGlobSetting().apiUrl + '/upload'
  const isLocating = ref(false)

  // 添加用户选择相关数据
  const usersList = ref<UserModel[]>([])
  const usersLoading = ref(false)
  const selectedUser = ref<Partial<UserModel> | null>(null)
  const userSearchKeyword = ref('')

  // 等级提示文字
  const levelTooltips = [
    'Level 1',
    'Level 2',
    'Level 3',
    'Level 4',
    'Level 5',
    'Level 6',
    'Level 7',
  ]

  const headers = {
    Authorization: `Bearer ${getToken()}`,
  }

  // Google Maps references
  let map: any = null
  let marker: any = null
  let autocomplete: any = null
  let geocoder: any = null
  let infoWindow: any = null
  let googleMapsLoaded = false

  // Form state
  const formState = reactive({
    title: '',
    type: 'normal' as 'normal' | 'specialGuest',
    userId: undefined as number | undefined,
    startDate: null as any,
    address: '',
    longitude: 0,
    latitude: 0,
    maxMembers: 10,
    description: '',
    photos: [] as { id: number; url: string; formats?: any }[],
    previewImage: null as { id: number; url: string; formats?: any } | null, // 添加预览图片字段
    content: '', // 添加content字段用于存储Vditor内容
  })

  // Form validation rules
  const rules = {
    title: [{ required: true, message: 'Please enter event title', trigger: 'blur' }],
    type: [{ required: true, message: 'Please select event type', trigger: 'change' }],
    userId: [{ required: true, message: 'Please select a guest user', trigger: 'change' }],
    startDate: [
      { required: true, message: 'Please select event date and time', trigger: 'change' },
    ],
    address: [{ required: true, message: 'Please enter event address', trigger: 'blur' }],
    maxMembers: [
      { required: true, message: 'Please enter maximum number of attendees', trigger: 'change' },
    ],
  }

  // 监听类型变化，当类型为 normal 时，清除 userId
  watch(
    () => formState.type,
    (newType) => {
      if (newType === 'normal') {
        formState.userId = undefined
        selectedUser.value = null
      } else if (newType === 'specialGuest' && usersList.value.length === 0) {
        // 如果是特殊嘉宾类型且用户列表为空，则加载用户列表
        fetchUsers()
      }
    },
  )

  // 处理类型变化
  function handleTypeChange(value: 'normal' | 'specialGuest') {
    if (value === 'specialGuest' && usersList.value.length === 0) {
      fetchUsers()
    }
  }

  // 过滤用户选项
  const filterUserOption: SelectProps['filterOption'] = (input: string, option: any) => {
    const username = option.children?.[0]?.children || ''
    return username.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  // 处理用户搜索
  async function handleUserSearch(value: string) {
    if (!value) return

    userSearchKeyword.value = value
    if (value.length > 1) {
      await fetchUsers(value)
    }
  }

  // 获取用户列表
  async function fetchUsers(username = '') {
    try {
      usersLoading.value = true
      const params = {
        page: 1,
        pageSize: 100,
      }

      // 如果有用户名搜索条件，添加到请求参数中
      if (username) {
        Object.assign(params, { username })
      }

      const response = await getUsers(params)
      // 从API响应中提取用户数组
      usersList.value = response.results || []
    } catch (error) {
      console.error('Failed to fetch users', error)
      createMessage.error('Failed to fetch users')
    } finally {
      usersLoading.value = false
    }
  }

  // 处理用户选择变化
  function handleUserChange(userId: number) {
    const user = usersList.value.find((u) => u.id === userId)
    if (user) {
      selectedUser.value = user
    }
  }

  // Get current location
  function getCurrentLocation(): Promise<{ lat: number; lng: number }> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by your browser'))
        return
      }

      isLocating.value = true
      navigator.geolocation.getCurrentPosition(
        (position) => {
          isLocating.value = false
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          })
        },
        (error) => {
          isLocating.value = false
          console.error('Error getting location:', error.message)
          reject(error)
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        },
      )
    })
  }

  // Load Google Maps API
  function loadGoogleMapsApi() {
    if (window.google && window.google.maps) {
      initMap()
      return
    }

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyDYVHubZ2WBlBNMFvNttMuFsD-EW8o0th4&libraries=places&callback=initGoogleMaps`
    script.async = true
    script.defer = true

    // Define global callback
    window.initGoogleMaps = () => {
      googleMapsLoaded = true
      initMap()
    }

    document.head.appendChild(script)
  }

  // Initialize the map
  async function initMap() {
    if (!mapContainer.value || !window.google) return

    let defaultLocation = { lat: formState.latitude || 0, lng: formState.longitude || 0 }

    // Try to get current location if not in edit mode
    if (!isEdit.value) {
      try {
        const currentLocation = await getCurrentLocation()
        defaultLocation = currentLocation
        formState.latitude = currentLocation.lat
        formState.longitude = currentLocation.lng
      } catch (error) {
        message.warning('Unable to get your current location. Using default location instead.')
        console.error(error)
      }
    }

    // Create map instance with proper options
    map = new window.google.maps.Map(mapContainer.value, {
      center: defaultLocation,
      zoom: 13,
      mapTypeControl: false,
      fullscreenControl: false,
      streetViewControl: false,
      zoomControl: true,
      zoomControlOptions: {
        position: window.google.maps.ControlPosition.RIGHT_CENTER,
      },
    })

    // Create geocoder
    geocoder = new window.google.maps.Geocoder()

    // Initialize InfoWindow
    infoWindow = new window.google.maps.InfoWindow({
      maxWidth: 320,
    })

    // Create marker
    marker = new window.google.maps.Marker({
      position: defaultLocation,
      map: map,
      draggable: true,
      animation: window.google.maps.Animation.DROP,
    })

    // Initialize autocomplete
    initAutocomplete()

    // Add event listener for marker drag
    marker?.addListener('dragend', () => {
      if (!marker) return
      const position = marker.getPosition()
      updateLocation(position.lat(), position.lng())
    })

    // Add click event for marker with info window
    marker?.addListener('click', () => {
      if (!marker || !infoWindow) return
      infoWindow.setContent(`
        <div>
          <strong>Selected Location</strong><br>
          ${formState.address || 'No address found'}<br>
          <small>Latitude: ${formState.latitude.toFixed(
            6,
          )}, Longitude: ${formState.longitude.toFixed(6)}</small>
        </div>
      `)
      infoWindow.open(map, marker)
    })

    // Add event listener for map click
    map?.addListener('click', (event: any) => {
      if (!marker || !infoWindow) return
      marker.setPosition(event.latLng)
      infoWindow.close()
      updateLocation(event.latLng.lat(), event.latLng.lng())
    })

    // If we already have coordinates, center the map there
    if (formState.latitude && formState.longitude) {
      const position = new window.google.maps.LatLng(formState.latitude, formState.longitude)
      marker?.setPosition(position)
      map?.setCenter(position)
    }

    // Reverse geocode the initial location to get address
    if (!formState.address && !isEdit.value) {
      updateLocation(defaultLocation.lat, defaultLocation.lng)
    } else if (formState.address && infoWindow) {
      // Show info window at initial position if address exists
      infoWindow.setContent(`
        <div>
          <strong>Selected Location</strong><br>
          ${formState.address}<br>
          <small>Latitude: ${formState.latitude.toFixed(
            6,
          )}, Longitude: ${formState.longitude.toFixed(6)}</small>
        </div>
      `)
      infoWindow.open(map, marker)
    }
  }

  // Initialize Places Autocomplete
  function initAutocomplete() {
    if (!searchInput.value || !window.google) return

    // Create the autocomplete object
    autocomplete = new window.google.maps.places.Autocomplete(searchInput.value, {
      fields: ['formatted_address', 'geometry', 'name', 'place_id'],
      types: ['establishment', 'geocode'],
    })

    // Bias the results to the current map's viewport
    if (autocomplete && map) {
      autocomplete.bindTo('bounds', map)
    }

    // Handle place selection
    autocomplete?.addListener('place_changed', () => {
      if (!autocomplete || !map || !marker || !infoWindow) return

      const place = autocomplete.getPlace()

      if (!place.geometry || !place.geometry.location) {
        // User entered the name of a Place that was not suggested and
        // pressed the Enter key, or the Place Details request failed.
        message.error('No details available for this place. Try a different search term.')
        return
      }

      // If the place has a geometry, update map and marker
      if (place.geometry.viewport) {
        map.fitBounds(place.geometry.viewport)
      } else {
        map.setCenter(place.geometry.location)
        map.setZoom(17)
      }

      marker.setPosition(place.geometry.location)

      // Update form values
      updateLocation(
        place.geometry.location.lat(),
        place.geometry.location.lng(),
        place.formatted_address,
      )

      // Show info window
      infoWindow.setContent(`
        <div>
          <strong>${place.name || 'Selected Location'}</strong><br>
          ${place.formatted_address || ''}<br>
          <small>Latitude: ${place.geometry.location
            .lat()
            .toFixed(6)}, Longitude: ${place.geometry.location.lng().toFixed(6)}</small>
        </div>
      `)
      infoWindow.open(map, marker)
    })
  }

  // Update location data in form state
  function updateLocation(lat: number, lng: number, address: string | null = null) {
    formState.latitude = lat
    formState.longitude = lng

    // If address is provided, use it directly
    if (address) {
      formState.address = address
      return
    }

    // Otherwise perform reverse geocoding
    if (geocoder) {
      type GeocoderResult = {
        formatted_address: string
        [key: string]: any
      }

      type GeocoderStatus =
        | 'OK'
        | 'ZERO_RESULTS'
        | 'OVER_QUERY_LIMIT'
        | 'REQUEST_DENIED'
        | 'INVALID_REQUEST'
        | 'UNKNOWN_ERROR'

      geocoder.geocode(
        { location: { lat, lng } },
        (results: GeocoderResult[], status: GeocoderStatus) => {
          if (status === 'OK' && results[0]) {
            formState.address = results[0].formatted_address

            // Update info window content
            if (infoWindow?.getMap()) {
              infoWindow.setContent(`
                <div>
                  <strong>Selected Location</strong><br>
                  ${results[0].formatted_address}<br>
                  <small>Latitude: ${lat.toFixed(6)}, Longitude: ${lng.toFixed(6)}</small>
                </div>
              `)
            }
          } else {
            console.warn('Geocoder failed due to: ' + status)
          }
        },
      )
    }
  }

  // My Location button click handler
  function handleMyLocationClick() {
    if (isLocating.value) return

    getCurrentLocation()
      .then((location) => {
        if (map && marker) {
          // Update the map
          const mapObj = map as any
          const markerObj = marker as any

          // Update the map
          mapObj.setCenter(location)
          mapObj.setZoom(15) // Zoom in a bit more
          markerObj.setPosition(location)

          // Update form with the location data
          updateLocation(location.lat, location.lng)

          // Close any open info window
          infoWindow?.close()
        }
      })
      .catch((error) => {
        message.error('Failed to get your location: ' + error.message)
      })
  }

  // File upload related functions
  function getBase64(file: File) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview && file.originFileObj) {
      file.preview = (await getBase64(file.originFileObj as File)) as string
    }
    previewImage.value = file.url || file.preview || ''
    previewVisible.value = true
  }

  const handleCancel = () => {
    previewVisible.value = false
  }

  // File upload validation
  function beforeUpload(file: File) {
    const isValidType = file.type.startsWith('image/') || file.type.startsWith('video/')
    const isLt2M = file.size / 1024 / 1024 < 2

    if (!isValidType) {
      message.error('You can only upload image or video files!')
    }
    if (!isLt2M) {
      message.error('File size must be less than 2MB!')
    }

    return isValidType && isLt2M
  }

  // Handle file upload change
  function handleUploadChange(info: UploadChangeParam) {
    fileList.value = info.fileList

    if (info.file.status === 'done') {
      // 服务器返回的数据结构
      const response = info.file.response[0]
      // 获取文件ID
      const id = response.id
      // 获取原始文件的uid
      const uid = info.file.originFileObj?.uid
      // 查找当前文件
      const currentFile = fileList.value.find((file) => file.uid === uid)

      if (currentFile) {
        // 更新文件的uid为服务器返回的id
        currentFile.uid = String(id)
        // 设置文件url为服务器返回的url
        currentFile.url = response.url
        // 添加预览url - 优先使用缩略图
        currentFile.thumbUrl = response.formats?.thumbnail?.url || response.url
        // 添加预览属性
        currentFile.preview = response.url
      }

      message.success(`${info.file.name} 上传成功`)

      // 更新表单数据，确保photos的结构与StarEventModel匹配
      formState.photos = fileList.value
        .filter((file) => file.status === 'done')
        .map((file) => {
          const fileResponse = file.response ? file.response[0] : null
          return {
            id: Number(file.uid), // 确保ID是数字类型
            url: fileResponse?.url || file.url || '',
            formats: fileResponse?.formats || {},
          }
        })
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`)
    }
  }

  // 修改的部分：Vditor实例和相关方法
  const vditor = ref<Vditor | null>(null)

  // 初始化Vditor编辑器
  function initVditor() {
    if (document.getElementById('vditor')) {
      const globSettings = useGlobSetting()

      vditor.value = new Vditor('vditor', {
        height: 800,
        placeholder: 'Please enter detailed content here...',
        mode: 'wysiwyg', // 所见即所得模式
        lang: 'en_US', // 设置语言为英文
        toolbarConfig: {
          pin: true,
        },
        // 精简工具栏，移除代码块、语音、全屏和更多等工具
        toolbar: [
          'headings',
          'bold',
          'italic',
          'strike',
          'link',
          '|',
          'list',
          'ordered-list',
          'check',
          'outdent',
          'indent',
          '|',
          'quote',
          'line',
          'table',
          'insert-before',
          'insert-after',
          'upload',
          '|',
          'undo',
          'redo',
          '|',
          'both',
        ],
        counter: {
          enable: true,
        },
        cache: {
          enable: false,
        },
        preview: {
          hljs: {
            enable: true,
            style: 'github',
          },
        },
        upload: {
          url: globSettings.apiUrl + '/upload',
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
          fieldName: 'files',
          max: 10 * 1024 * 1024, // 限制文件大小为10MB
          withCredentials: false,
          accept: 'image/*',
          format(files, responseText) {
            try {
              // 解析上传后的响应
              const res = JSON.parse(responseText)
              // 服务器返回的结构是一个数组，取第一个元素
              const file = res[0]

              if (file && file.url) {
                // 返回符合Vditor要求的格式
                return JSON.stringify({
                  msg: '',
                  code: 0,
                  data: {
                    errFiles: [],
                    succMap: {
                      [files[0].name]: file.url,
                    },
                  },
                })
              } else {
                return JSON.stringify({
                  msg: 'Upload failed',
                  code: -1,
                })
              }
            } catch (e: any) {
              console.error('Failed to parse upload response:', e)
              return JSON.stringify({
                msg: 'Upload failed: ' + e.message,
                code: -1,
              })
            }
          },
          success(_editor: any, msg: any) {
            try {
              // 检查 msg 是字符串还是直接是对象
              let fileData: any
              if (typeof msg === 'string') {
                try {
                  // 如果是字符串，尝试解析JSON
                  fileData = JSON.parse(msg)
                } catch (err) {
                  // 如果解析失败，可能是直接传递的响应对象
                  fileData = msg
                }
              } else {
                // 如果已经是对象，直接使用
                fileData = msg
              }

              // 如果是对象格式，检查是否包含文件URL
              if (fileData?.data?.succMap) {
                // 获取 Vditor 格式的返回
                const imgUrl = Object.values(fileData.data.succMap)[0]
                if (imgUrl) {
                  // 在编辑器中插入图片的 Markdown 格式
                  vditor?.value?.insertValue(`![image](${imgUrl})`)
                  message.success('图片上传成功')
                }
              } else if (Array.isArray(fileData) && fileData.length > 0) {
                // 直接是API返回的数组
                const file = fileData[0]
                if (file && file.url) {
                  // 将图片以 Markdown 语法插入编辑器
                  vditor?.value?.insertValue(`![${file.name || 'image'}](${file.url})`)
                  message.success('图片上传成功')

                  // 将图片添加到 formState.photos 以便表单提交
                  const newPhoto = {
                    id: Number(file.id),
                    url: file.url,
                    formats: file.formats || {},
                  }

                  // 检查是否已经存在相同ID的照片
                  const photoExists = formState.photos.some((photo) => photo.id === newPhoto.id)
                  if (!photoExists) {
                    formState.photos.push(newPhoto)
                  }
                }
              }
            } catch (e) {
              console.error('Error processing upload success:', e)
              message.error('图片处理失败')
            }
          },
          error(msg) {
            console.error('Upload error:', msg)
            message.error('Image upload failed: ' + msg)
          },
        },
        after: () => {
          if (vditor.value && formState.content) {
            vditor.value.setValue(formState.content)
          }
        },
        input: (value) => {
          formState.content = value
        },
      })
    }
  }

  onMounted(async () => {
    if (isEdit.value) {
      await fetchEventDetail()
    }

    // Load Google Maps after DOM is ready
    loadGoogleMapsApi()

    // 初始化Vditor编辑器
    initVditor()
  })

  onUnmounted(() => {
    // Clean up global callback
    if (window.initGoogleMaps) {
      delete window.initGoogleMaps
    }

    // Clean up event listeners
    if (window.google && map) {
      window.google.maps.event.clearInstanceListeners(map)
    }
    if (marker) {
      window.google.maps.event.clearInstanceListeners(marker)
    }
    if (autocomplete) {
      window.google.maps.event.clearInstanceListeners(autocomplete)
    }

    // 销毁Vditor实例
    if (vditor.value) {
      vditor.value.destroy()
    }
  })

  // Fetch event details for editing
  async function fetchEventDetail() {
    try {
      const id = Number(route.params.id)
      const eventData = await getStarEvent(id)

      // Set form values
      Object.keys(formState).forEach((key) => {
        if (key === 'startDate' && eventData[key]) {
          formState[key] = dayjs(eventData[key])
        } else if (key === 'photos' && eventData.photos) {
          formState[key] = eventData.photos.map((item: any) => ({
            id: item.id,
            url: item.url,
            formats: item.formats || {},
          }))

          fileList.value = eventData.photos.map((item: any, index: number) => ({
            uid: String(item.id || -index),
            name: item.name || `image-${index}`,
            status: 'done',
            url: item.url,
            thumbUrl: item.formats?.thumbnail?.url || item.url,
            preview: item.url,
            response: [item], // 包装为数组以匹配上传响应格式
          }))
        } else if (key === 'content') {
          // 处理content字段，可能在API中不存在
          if (eventData.content !== undefined) {
            // @ts-ignore - content字段可能在API类型中未定义，但实际返回数据中存在
            formState.content = eventData.content
            // 如果Vditor已经初始化，则设置内容
            if (vditor.value) {
              // @ts-ignore - content字段可能在API类型中未定义，但实际返回数据中存在
              vditor.value.setValue(eventData.content)
            }
          }
        } else if (key === 'userId' && eventData.type === 'specialGuest') {
          // 处理 star 字段到 userId 的映射
          formState.userId = eventData.star?.id || undefined
          // 如果有 star 值，尝试获取用户信息
          if (eventData.star?.id && !selectedUser.value) {
            // 如果已经获取到了用户列表，检查是否能在列表中找到该用户
            if (usersList.value.length) {
              const existingUser = usersList.value.find((u) => u.id === eventData.star!.id)
              if (existingUser) {
                // 如果能找到，直接设置选中
                handleUserChange(eventData.star.id)
              } else {
                // 如果现有列表中找不到，说明可能需要重新获取或者加载更多用户
                // 可以直接使用API返回的star对象作为selectedUser
                selectedUser.value = eventData.star as any
                // 并尝试重新加载用户列表以获取完整信息
                fetchUsers()
              }
            } else {
              // 用户列表为空，先获取用户列表，然后尝试设置选中的用户
              fetchUsers().then(() => {
                // 获取列表后检查是否能找到对应用户
                const foundUser = usersList.value.find((u) => u.id === eventData.star!.id)
                if (foundUser) {
                  handleUserChange(eventData.star!.id)
                } else {
                  // 如果仍然找不到，使用API返回的star对象
                  selectedUser.value = eventData.star as any
                }
              })
            }
          }
        } else {
          // 对于其他字段直接赋值
          // @ts-ignore
          formState[key] = eventData[key]
        }
      })

      // Update map if coordinates are available
      if (googleMapsLoaded && eventData.latitude && eventData.longitude) {
        const position = new window.google.maps.LatLng(eventData.latitude, eventData.longitude)
        if (marker) {
          const markerObj = marker as any
          markerObj.setPosition(position)

          // Show info window
          if (infoWindow && formState.address) {
            const infoWindowObj = infoWindow as any
            infoWindowObj.setContent(`
              <div>
                <strong>Selected Location</strong><br>
                ${formState.address}<br>
                <small>Latitude: ${formState.latitude.toFixed(
                  6,
                )}, Longitude: ${formState.longitude.toFixed(6)}</small>
              </div>
            `)
            infoWindowObj.open(map, marker)
          }
        }
        if (map) {
          const mapObj = map as any
          mapObj.setCenter(position)
        }
      }
    } catch (error) {
      console.error('Failed to fetch event details', error)
      createMessage.error('Failed to fetch event details')
    }
  }

  // Submit form
  async function submitForm() {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      submitLoading.value = true

      // 获取Vditor的内容
      if (vditor.value) {
        formState.content = vditor.value.getValue()
      }

      // Process form data with correct types
      const formData = {
        ...formState,
        startDate: formState.startDate ? formState.startDate.toISOString() : undefined,
        // 确保photos格式正确
        photos: fileList.value
          ? fileList.value
              .filter((file) => file.status === 'done')
              .map((file) => {
                const fileResponse = file.response ? file.response[0] : null
                return {
                  id: Number(file.uid), // 确保ID是数字类型
                  url: fileResponse?.url || file.url || '',
                  formats: fileResponse?.formats || {},
                }
              })
          : [],
        // 确保previewImage格式正确
        previewImage: formState.previewImage
          ? {
              id: formState.previewImage.id,
              url: formState.previewImage.url,
              formats: formState.previewImage.formats || {},
            }
          : null,
        content: formState.content, // 确保内容被包含在提交数据中
      }

      // 如果类型是 specialGuest，将 userId 字段重命名为 star
      if (formState.type === 'specialGuest' && formState.userId) {
        // 只传递用户ID，不要将整个用户对象传递给API
        // @ts-ignore - 动态添加star字段，TypeScript无法识别
        formData.star = formState.userId
        delete formData.userId
      }

      if (isEdit.value) {
        const id = Number(route.params.id)
        await updateStarEvent(id, formData)
        createMessage.success('Event updated successfully')
      } else {
        await createStarEvent(formData)
        createMessage.success('Event created successfully')
      }
      goBack()
    } catch (error) {
      console.error('Submission failed', error)
      createMessage.error(typeof error === 'string' ? error : 'Submission failed')
    } finally {
      submitLoading.value = false
    }
  }

  // Go back to list
  function goBack() {
    router.push('/star-event/list')
  }
</script>

<style scoped>
  /* 响应式布局 */
  @media (max-width: 1200px) {
    .form-layout {
      flex-direction: column;
    }

    .form-left,
    .form-right {
      width: 100%;
    }

    .form-right {
      min-width: auto;
    }
  }

  .form-container {
    width: 100%;
  }

  .form-card {
    width: 100%;
    margin-bottom: 24px;
    border-radius: 8px;
    position: relative;
    padding-bottom: 60px;
  }

  /* 创建左右布局 */
  .form-layout {
    display: flex;
    gap: 24px;
  }

  .form-left {
    flex: 1;
  }

  .form-right {
    flex: 1;
    min-width: 400px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* Vditor编辑器容器样式 */
  .vditor-container {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    height: 100%;
    min-height: 600px;
  }

  .content-form-item {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }

  :deep(.content-form-item .ant-form-item-control) {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :deep(.content-form-item .ant-form-item-control-input) {
    flex: 1;
    height: 100%;
  }

  :deep(.content-form-item .ant-form-item-control-input-content) {
    height: 100%;
  }

  .form-actions {
    margin-top: 24px;
    text-align: right;
    position: absolute;
    bottom: 20px;
    right: 24px;
  }

  /* 确保表单项内容左对齐 */
  :deep(.ant-form-item-control-input) {
    text-align: left;
  }

  /* 缩短表单项标签宽度 */
  :deep(.ant-form-item-label) {
    min-width: 80px;
  }

  /* 优化表单项间距 */
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }

  /* 用户信息卡片样式 */
  .user-info-card {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 12px;
    margin-top: 8px;
  }

  .user-info-item {
    display: flex;
    margin-bottom: 8px;
  }

  .user-info-item:last-child {
    margin-bottom: 0;
  }

  .user-info-label {
    width: 60px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }

  .user-info-value {
    flex: 1;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
  }

  .level-stars {
    display: flex;
    align-items: center;
  }

  .level-text {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .mt-2 {
    margin-top: 8px;
  }

  /* Location container styling */
  .location-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  /* Map wrapper styling */
  .map-wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 12px;
  }

  /* Map container styling */
  .map-container {
    width: 100%;
    height: 320px;
    border-radius: 4px;
    border: 1px solid #f0f0f0;
  }

  /* Map controls overlay styling */
  .map-controls-overlay {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
    z-index: 1;
    align-items: center;
  }

  /* Search box styling */
  .search-box-container {
    flex: 1;
  }

  .location-search-input {
    width: 100%;
    padding: 6px 10px;
    font-size: 13px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-sizing: border-box;
    transition: all 0.3s;
    background-color: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  .location-search-input:focus {
    border-color: #40a9ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  /* My Location button styling */
  .my-location-button {
    white-space: nowrap;
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
  }

  /* Coordinates display styling */
  .coordinates-display {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    z-index: 1;
  }

  .ml-4 {
    margin-left: 16px;
  }
</style>
