<template>
  <div class="marketing-container">
    <div class="container">
      <h1>{{ getTitle() }}</h1>
      <p class="subtitle">{{ getSubtitle() }}</p>

      <!-- English Content -->
      <div v-if="currentLocale === 'en'">
        <div class="intro-section">
          <p class="intro-text"
            >Starchex Marketing is a professional marketing agency dedicated to providing clients
            with innovative and effective digital marketing solutions.</p
          >
        </div>

        <h2>Our Mission</h2>
        <p
          >Our mission is to maximize our clients' brand value, strengthen their market position,
          and enable sustainable growth.</p
        >

        <h2>What We Do</h2>
        <div class="services-grid">
          <div class="service-card">
            <h3>Digital Advertising Campaigns</h3>
            <p
              >Planning and execution of comprehensive digital advertising campaigns across multiple
              platforms.</p
            >
          </div>
          <div class="service-card">
            <h3>Brand Strategy Development</h3>
            <p
              >Creating compelling brand strategies that resonate with your target audience and
              drive engagement.</p
            >
          </div>
          <div class="service-card">
            <h3>Social Media Management</h3>
            <p
              >Content creation and management for social media platforms to build community and
              drive conversions.</p
            >
          </div>
          <div class="service-card">
            <h3>Market Analysis & Insights</h3>
            <p
              >Providing comprehensive market analysis and consumer insights to inform strategic
              decisions.</p
            >
          </div>
          <div class="service-card">
            <h3>Performance Marketing</h3>
            <p
              >Results-driven marketing solutions focused on measurable outcomes and ROI
              optimization.</p
            >
          </div>
        </div>

        <h2>Why Choose Starchex?</h2>
        <p
          >We develop customized strategies based on our experience collaborating with clients
          across various industries. We thoroughly analyze client needs and deliver optimal results
          through creative, data-driven marketing approaches.</p
        >

        <div class="marketing-consent-section">
          <h2>Marketing Consent</h2>
          <p
            >By using Starchex, you agree to the following terms and conditions regarding marketing
            communications. We may send you marketing materials, such as emails or push
            notifications, from time to time. These materials may contain advertisements or
            promotional offers for our products or services.</p
          >

          <p
            >We may also use your personal information for marketing purposes, including but not
            limited to, using your name, profile picture, and other identifying information to
            tailor our marketing efforts to your interests. We will only share your personal
            information with third-party service providers who assist us in delivering our marketing
            materials and providing our services.</p
          >

          <p
            >We may also use cookies and similar technologies to collect data about your use of our
            platform and your interaction with our content. This data may be used to improve our
            platform and provide more relevant content to you.</p
          >

          <p
            >If you do not wish to receive these materials, please unsubscribe from our marketing
            list by following the instructions provided in each email or push notification.</p
          >
        </div>

        <h2>Contact Us</h2>
        <div class="contact-info">
          <p
            ><strong
              >For detailed information or consultation, please contact us anytime:</strong
            ></p
          >
          <p>Email: <EMAIL></p>
          <p>Address: Starchex Marketing Center, Gangnam-gu, Seoul, Republic of Korea</p>
        </div>
      </div>

      <!-- Korean Content -->
      <div v-else-if="currentLocale === 'ko'">
        <div class="intro-section">
          <p class="intro-text"
            >Starchex 마케팅은 고객에게 독창적이고 효과적인 디지털 마케팅 솔루션을 제공하는 데
            전념하는 전문 마케팅 기관입니다.</p
          >
        </div>

        <h2>우리의 사명</h2>
        <p
          >당사의 사명은 고객의 브랜드 가치를 극대화하고, 시장에서의 입지를 강화하며, 지속 가능한
          성장을 가능하게 하는 것입니다.</p
        >

        <h2>우리가 하는 일</h2>
        <div class="services-grid">
          <div class="service-card">
            <h3>디지털 광고 캠페인</h3>
            <p>다양한 플랫폼에서 포괄적인 디지털 광고 캠페인을 기획하고 실행합니다.</p>
          </div>
          <div class="service-card">
            <h3>브랜드 전략 개발</h3>
            <p>타겟 고객에게 어필하고 참여를 유도하는 매력적인 브랜드 전략을 개발합니다.</p>
          </div>
          <div class="service-card">
            <h3>소셜 미디어 운영</h3>
            <p>커뮤니티 구축과 전환율 향상을 위한 소셜 미디어 콘텐츠 제작 및 운영을 담당합니다.</p>
          </div>
          <div class="service-card">
            <h3>시장 분석 및 인사이트</h3>
            <p>전략적 의사결정을 위한 포괄적인 시장 분석과 소비자 인사이트를 제공합니다.</p>
          </div>
          <div class="service-card">
            <h3>성과 기반 마케팅</h3>
            <p>측정 가능한 결과와 ROI 최적화에 중점을 둔 결과 중심의 마케팅 솔루션을 제공합니다.</p>
          </div>
        </div>

        <h2>왜 Starchex 인가?</h2>
        <p
          >우리는 다양한 업계의 고객과 협업한 경험을 바탕으로 맞춤형 전략을 수립합니다. 고객의
          요구를 철저히 분석하고, 창의적이며 데이터 기반의 마케팅 접근법을 통해 최상의 결과를
          도출합니다.</p
        >

        <div class="marketing-consent-section">
          <h2>마케팅 동의</h2>
          <p
            >Starchex를 사용함으로써 마케팅 커뮤니케이션에 관한 다음 약관에 동의하게 됩니다. 저희는
            때때로 이메일이나 푸시 알림과 같은 마케팅 자료를 보낼 수 있습니다. 이러한 자료에는 저희
            제품이나 서비스에 대한 광고나 프로모션 제안이 포함될 수 있습니다.</p
          >

          <p
            >또한 귀하의 이름, 프로필 사진 및 기타 식별 정보를 사용하여 귀하의 관심사에 맞게 마케팅
            활동을 조정하는 등 마케팅 목적으로 개인정보를 사용할 수 있습니다. 마케팅 자료 전달 및
            서비스 제공을 지원하는 제3자 서비스 제공업체와만 개인정보를 공유합니다.</p
          >

          <p
            >또한 쿠키 및 유사한 기술을 사용하여 플랫폼 사용 및 콘텐츠와의 상호작용에 대한 데이터를
            수집할 수 있습니다. 이 데이터는 플랫폼을 개선하고 더 관련성 높은 콘텐츠를 제공하는 데
            사용될 수 있습니다.</p
          >

          <p
            >이러한 자료를 받고 싶지 않으시면 각 이메일이나 푸시 알림에 제공된 지침에 따라 마케팅
            목록에서 구독을 취소해 주세요.</p
          >
        </div>

        <h2>문의하기</h2>
        <div class="contact-info">
          <p><strong>자세한 정보나 상담을 원하시면 언제든지 저희에게 연락주세요:</strong></p>
          <p>이메일: <EMAIL></p>
          <p>주소: 대한민국 서울특별시 강남구 Starchex 마케팅센터</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { useLocale } from '/@/locales/useLocale'

  const route = useRoute()
  const { changeLocale } = useLocale()

  // Get current locale from route or meta
  const currentLocale = computed(() => {
    const routeLocale = route.meta?.locale as string
    if (routeLocale) {
      return routeLocale
    }

    // Extract locale from path if available
    const pathSegments = route.path.split('/')
    if (pathSegments.length >= 3 && (pathSegments[2] === 'en' || pathSegments[2] === 'ko')) {
      return pathSegments[2]
    }

    return 'en' // default to English
  })

  // Get localized text
  const getTitle = () => {
    return currentLocale.value === 'ko' ? 'Starchex 마케팅에 대해' : 'About Starchex Marketing'
  }

  const getSubtitle = () => {
    return currentLocale.value === 'ko'
      ? '전문적이고 혁신적인 디지털 마케팅 솔루션'
      : 'Professional and Innovative Digital Marketing Solutions'
  }

  // Set locale when component mounts
  onMounted(async () => {
    const locale = currentLocale.value
    if (locale && ['en', 'ko', 'vi'].includes(locale)) {
      await changeLocale(locale as any)
    }
  })
</script>

<style scoped>
  .marketing-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
  }

  .container {
    max-width: 900px;
    width: 100%;
    margin: 0 auto;
    padding: 40px 30px;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-top: 40px;
    margin-bottom: 40px;
    box-sizing: border-box;
  }

  h1 {
    color: #1890ff;
    margin-bottom: 20px;
    font-size: 2.2rem;
    font-weight: 600;
    line-height: 1.3;
  }

  .subtitle {
    color: #666;
    font-size: 1.2rem;
    margin-bottom: 30px;
    font-style: italic;
    text-align: center;
  }

  .intro-section {
    background: linear-gradient(135deg, #fff7e6 0%, #ffeaa7 100%);
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border-left: 4px solid #f39c12;
  }

  .intro-text {
    font-size: 1.1rem;
    line-height: 1.7;
    margin: 0;
    color: #2c3e50;
  }

  h2 {
    color: #262626;
    margin-top: 35px;
    margin-bottom: 20px;
    font-size: 1.6rem;
    font-weight: 500;
    border-bottom: 2px solid #f39c12;
    padding-bottom: 8px;
  }

  h3 {
    color: #f39c12;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 500;
  }

  p {
    margin-bottom: 18px;
    text-align: left;
    line-height: 1.7;
  }

  .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 25px 0;
  }

  .service-card {
    background-color: #fafafa;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e8e8e8;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .marketing-consent-section {
    background-color: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    margin: 30px 0;
    border-left: 4px solid #dc3545;
  }

  .marketing-consent-section h2 {
    color: #dc3545;
    border-bottom-color: #dc3545;
    margin-top: 0;
  }

  .contact-info {
    background-color: #f0f9ff;
    padding: 20px;
    border-radius: 6px;
    border-left: 4px solid #1890ff;
    margin-top: 30px;
  }

  @media (max-width: 768px) {
    .marketing-container {
      padding: 10px;
    }

    .container {
      margin: 0;
      margin-top: 10px;
      margin-bottom: 10px;
      padding: 25px 20px;
      border-radius: 8px;
      max-width: none;
      width: 100%;
    }

    h1 {
      font-size: 1.8rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    h2 {
      font-size: 1.4rem;
    }

    h3 {
      font-size: 1.2rem;
    }

    .services-grid {
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .intro-section,
    .marketing-consent-section {
      padding: 20px;
    }

    .service-card {
      padding: 15px;
    }
  }

  @media (max-width: 480px) {
    .marketing-container {
      padding: 5px;
    }

    .container {
      margin: 0;
      padding: 20px 15px;
      border-radius: 6px;
    }

    h1 {
      font-size: 1.6rem;
    }

    .subtitle {
      font-size: 0.9rem;
    }

    h2 {
      font-size: 1.3rem;
    }

    h3 {
      font-size: 1.1rem;
    }

    p {
      font-size: 0.9rem;
    }

    .intro-section,
    .marketing-consent-section {
      padding: 15px;
    }

    .service-card {
      padding: 12px;
    }

    .contact-info {
      padding: 15px;
    }
  }
</style>
