<template>
  <div class="about-container">
    <div class="container">
      <h1>{{ getTitle() }}</h1>

      <div class="nav-links">
        <router-link :to="getLocalizedPath('/privacy')" class="nav-link">
          {{ getPrivacyText() }}
        </router-link>
        <router-link :to="getLocalizedPath('/terms')" class="nav-link">
          {{ getTermsText() }}
        </router-link>
        <router-link :to="getLocalizedPath('/about-detail')" class="nav-link">
          {{ getAboutDetailText() }}
        </router-link>
        <router-link :to="getLocalizedPath('/marketing')" class="nav-link">
          {{ getMarketingText() }}
        </router-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { message } from 'ant-design-vue'
  import { useLocale } from '/@/locales/useLocale'

  const route = useRoute()
  const { changeLocale } = useLocale()

  // Get current locale from route or meta
  const currentLocale = computed(() => {
    const routeLocale = route.meta?.locale as string
    if (routeLocale) {
      return routeLocale
    }

    // Extract locale from path if available
    const pathSegments = route.path.split('/')
    if (pathSegments.length >= 3 && (pathSegments[2] === 'en' || pathSegments[2] === 'ko')) {
      return pathSegments[2]
    }

    return 'en' // default to English
  })

  // Generate localized path
  const getLocalizedPath = (subPath: string) => {
    const locale = currentLocale.value
    if (locale === 'en') {
      return `/about/en${subPath}`
    } else if (locale === 'ko') {
      return `/about/ko${subPath}`
    }
    return `/about${subPath}` // default path
  }

  // Get localized text
  const getTitle = () => {
    return currentLocale.value === 'ko' ? 'Starchex 소개' : 'About Starchex'
  }

  const getSubtitle = () => {
    return currentLocale.value === 'ko'
      ? '관리자 플랫폼에 오신 것을 환영합니다'
      : 'Welcome to the Admin Platform'
  }

  const getPrivacyText = () => {
    return currentLocale.value === 'ko' ? '개인정보 처리방침' : 'Privacy Policy'
  }

  const getTermsText = () => {
    return currentLocale.value === 'ko' ? '이용 약관' : 'Terms of Service'
  }

  const getAboutDetailText = () => {
    return currentLocale.value === 'ko' ? 'Starchex 소개' : 'About Starchex'
  }

  const getMarketingText = () => {
    return currentLocale.value === 'ko' ? '마케팅 서비스' : 'Marketing Services'
  }

  const getContactText = () => {
    return currentLocale.value === 'ko' ? '문의하기' : 'Contact Us'
  }

  const getComingSoonText = () => {
    return currentLocale.value === 'ko' ? '곧 출시 예정' : 'Coming Soon'
  }

  const showComingSoon = (pageName: string) => {
    message.info(`${pageName} ${getComingSoonText()}`)
  }

  // Set locale when component mounts
  onMounted(async () => {
    const locale = currentLocale.value
    if (locale && ['en', 'ko', 'vi'].includes(locale)) {
      await changeLocale(locale as any)
    }
  })
</script>

<style scoped>
  .about-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  }

  .container {
    max-width: 600px;
    width: 100%;
    background-color: white;
    padding: 60px 40px;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    box-sizing: border-box;
  }

  .logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    border-radius: 50%;
    margin: 0 auto 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    font-weight: bold;
  }

  h1 {
    color: #262626;
    margin-bottom: 20px;
    font-size: 2.5rem;
    font-weight: 600;
  }

  .subtitle {
    color: #8c8c8c;
    margin-bottom: 40px;
    font-size: 1.1rem;
  }

  .nav-links {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 40px;
  }

  .nav-link {
    display: inline-block;
    padding: 15px 30px;
    background-color: #f8f9fa;
    color: #1890ff;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    text-align: center;
  }

  .nav-link:hover {
    background-color: #1890ff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
  }

  .back-link {
    color: #8c8c8c;
    text-decoration: none;
    font-size: 0.9rem;
  }

  .back-link:hover {
    color: #1890ff;
    text-decoration: underline;
  }

  @media (min-width: 768px) {
    .nav-links {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }
  }

  @media (min-width: 1024px) {
    .nav-links {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1200px) {
    .nav-links {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 768px) {
    .about-container {
      padding: 10px;
    }

    .container {
      margin: 0;
      padding: 30px 20px;
      border-radius: 12px;
      max-width: none;
      width: 100%;
    }

    h1 {
      font-size: 2rem;
    }

    .nav-links {
      gap: 10px;
    }

    .nav-link {
      padding: 12px 20px;
      font-size: 0.9rem;
    }
  }

  @media (max-width: 480px) {
    .about-container {
      padding: 5px;
    }

    .container {
      padding: 20px 15px;
      border-radius: 8px;
    }

    h1 {
      font-size: 1.8rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    .nav-link {
      padding: 10px 15px;
      font-size: 0.85rem;
    }

    .logo {
      width: 60px;
      height: 60px;
      font-size: 1.5rem;
    }
  }
</style>
