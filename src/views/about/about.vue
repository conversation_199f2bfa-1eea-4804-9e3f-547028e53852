<template>
  <div class="about-detail-container">
    <div class="container">
      <h1>{{ getTitle() }}</h1>
      <p class="subtitle">{{ getSubtitle() }}</p>

      <!-- English Content -->
      <div v-if="currentLocale === 'en'">
        <div class="intro-section">
          <p class="intro-text"
            >Starchex is an offline connection platform designed for high-value individuals. Through
            "Weekly Star" featured interviews and premium private gatherings, it connects people of
            influence, experience, or resources with those who seek collaboration. It's more than a
            social tool — it's a network for cooperation, conversation, and co-creation.</p
          >
        </div>

        <h2>Product Positioning: Human-Centered Real Connection</h2>
        <p
          >Starchex is committed to providing an authentic and high-trust communication environment.
          We believe that offline connections are the most effective way to build long-term
          value-based relationships. The platform enhances understanding and trust through real
          identity verification, behavior records, and feedback from offline interactions.</p
        >

        <h2>Key Features</h2>

        <div class="feature-section">
          <h3>1. Weekly Star Interview System</h3>
          <p
            >Each week, a representative influencer is featured in an in-depth interview. This
            provides potential participants with a comprehensive understanding of their background,
            views, and values — serving as a key entry point before attending any offline
            gathering.</p
          >
        </div>

        <div class="feature-section">
          <h3>2. Private Gathering Management</h3>
          <p
            >Gatherings are initiated by influencers. The platform supports registration, applicant
            screening, status tracking, and final confirmation — ensuring every event offers
            meaningful connection opportunities. Key details like time, location, and participation
            progress are clearly displayed.</p
          >
        </div>

        <div class="feature-section">
          <h3>3. Community Module</h3>
          <p
            >Starchex includes a premium anonymous community where users can post, ask questions, or
            share insights freely. It's ideal for discussing collaboration, career insights, and
            resource matchmaking. Anonymity protects privacy, while the underlying real-name
            identity system ensures order.</p
          >
        </div>

        <h2>Typical Use Cases</h2>
        <ul class="use-cases">
          <li>Entrepreneurs seeking investment or experienced mentors</li>
          <li>Business leaders looking for potential partners or collaborators</li>
          <li>Professionals exploring new industries or seeking inspiration</li>
          <li
            >Influential thought leaders and creators managing invitations and choosing dialogue
            partners</li
          >
        </ul>

        <h2>Vision and Future Direction</h2>
        <p
          >Starchex aims to become Asia's leading offline high-value networking platform. In the
          future, it will expand to include more vertical industry interviews, strengthen community
          collaboration, and use AI-powered matching to optimize gathering selection — making it
          easier and more efficient to "find people worth meeting in person."</p
        >
      </div>

      <!-- Korean Content -->
      <div v-else-if="currentLocale === 'ko'">
        <div class="intro-section">
          <p class="intro-text"
            >Starchex는 고가치 인물들을 위한 오프라인 연결 플랫폼입니다. 'Weekly Star' 인터뷰와
            프라이빗 모임을 통해 영향력, 경험 또는 자원을 가진 사람들과 그들과의 협업을 원하는
            사용자를 연결합니다. 단순한 소셜 도구가 아니라, 협력과 대화, 공동 창작을 위한
            네트워크입니다.</p
          >
        </div>

        <h2>제품 포지셔닝: 사람 중심의 진정한 연결</h2>
        <p
          >Starchex는 진정성 있고 신뢰할 수 있는 소통 공간을 제공합니다. 우리는 오프라인 연결이
          장기적인 가치 관계를 구축하는 가장 효과적인 방식이라고 믿습니다. 실명 인증, 행동 기록,
          오프라인 피드백 등을 통해 신뢰와 이해의 효율을 높입니다.</p
        >

        <h2>핵심 기능</h2>

        <div class="feature-section">
          <h3>1. Weekly Star 인터뷰 시스템</h3>
          <p
            >매주 대표 인플루언서를 선정하여 심층 인터뷰 콘텐츠를 제공합니다. 참여자는 모임에 앞서
            인물의 배경, 가치관, 관점을 깊이 있게 파악할 수 있습니다.</p
          >
        </div>

        <div class="feature-section">
          <h3>2. 프라이빗 모임 관리</h3>
          <p
            >인플루언서가 주최하는 모임은 플랫폼을 통해 신청, 선별, 상태 추적, 최종 확정까지
            체계적으로 관리됩니다. 시간, 장소, 진행 상황 등의 핵심 정보가 실시간으로 표시됩니다.</p
          >
        </div>

        <div class="feature-section">
          <h3>3. Community 커뮤니티 모듈</h3>
          <p
            >Starchex는 고급 익명 커뮤니티를 내장하고 있어 사용자들이 자유롭게 글을 올리고
            질문하거나 의견을 표현할 수 있습니다. 협업 기회, 직장 통찰, 자원 연계 등 다양한 주제를
            논의하기 적합합니다. 익명성은 프라이버시를 보호하며, 실명 기반 시스템으로 질서를
            유지합니다.</p
          >
        </div>

        <h2>주요 사용 사례</h2>
        <ul class="use-cases">
          <li>투자자나 멘토를 찾는 스타트업 창업자</li>
          <li>유망한 파트너나 협업 대상을 찾는 기업 결정권자</li>
          <li>새로운 산업을 탐색하거나 협업 아이디어를 찾는 직장인</li>
          <li>초대 관리를 체계적으로 하고 싶은 인플루언서 및 창작자</li>
        </ul>

        <h2>비전과 향후 방향</h2>
        <p
          >Starchex는 아시아를 대표하는 고가치 오프라인 네트워킹 플랫폼이 되는 것을 목표로 합니다.
          앞으로는 산업별 인터뷰를 확대하고, 커뮤니티 협업 메커니즘을 강화하며, AI 기반 매칭으로
          모임 선별 효율을 높여 '직접 만나볼 가치가 있는 사람'을 더 쉽게 찾을 수 있게 할
          것입니다.</p
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { useLocale } from '/@/locales/useLocale'

  const route = useRoute()
  const { changeLocale } = useLocale()

  // Get current locale from route or meta
  const currentLocale = computed(() => {
    const routeLocale = route.meta?.locale as string
    if (routeLocale) {
      return routeLocale
    }

    // Extract locale from path if available
    const pathSegments = route.path.split('/')
    if (pathSegments.length >= 3 && (pathSegments[2] === 'en' || pathSegments[2] === 'ko')) {
      return pathSegments[2]
    }

    return 'en' // default to English
  })

  // Get localized text
  const getTitle = () => {
    return currentLocale.value === 'ko'
      ? 'Starchex: 고급 인플루언서를 위한 오프라인 연결 플랫폼'
      : 'Starchex: An Offline Connection Platform for High-Influence Individuals'
  }

  const getSubtitle = () => {
    return currentLocale.value === 'ko'
      ? '인플루언서와 협업을 원하는 사람들을 위한 오프라인 소셜 무대'
      : 'A social stage for influencers and collaboration seekers'
  }

  // Set locale when component mounts
  onMounted(async () => {
    const locale = currentLocale.value
    if (locale && ['en', 'ko', 'vi'].includes(locale)) {
      await changeLocale(locale as any)
    }
  })
</script>

<style scoped>
  .about-detail-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
  }

  .container {
    max-width: 900px;
    width: 100%;
    margin: 0 auto;
    padding: 40px 30px;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-top: 40px;
    margin-bottom: 40px;
    box-sizing: border-box;
  }

  h1 {
    color: #1890ff;
    margin-bottom: 20px;
    font-size: 2.2rem;
    font-weight: 600;
    line-height: 1.3;
  }

  .subtitle {
    color: #666;
    font-size: 1.2rem;
    margin-bottom: 30px;
    font-style: italic;
    text-align: center;
  }

  .intro-section {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border-left: 4px solid #1890ff;
  }

  .intro-text {
    font-size: 1.1rem;
    line-height: 1.7;
    margin: 0;
    color: #2c3e50;
  }

  h2 {
    color: #262626;
    margin-top: 35px;
    margin-bottom: 20px;
    font-size: 1.6rem;
    font-weight: 500;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 8px;
  }

  h3 {
    color: #1890ff;
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 500;
  }

  p {
    margin-bottom: 18px;
    text-align: left;
    line-height: 1.7;
  }

  .feature-section {
    background-color: #fafafa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e8e8e8;
  }

  .feature-section h3 {
    margin-top: 0;
    color: #1890ff;
  }

  .use-cases {
    background-color: #f9f9f9;
    padding: 20px 25px;
    border-radius: 8px;
    margin: 20px 0;
  }

  .use-cases li {
    margin-bottom: 12px;
    padding-left: 10px;
    position: relative;
  }

  .use-cases li::before {
    content: '▶';
    color: #1890ff;
    position: absolute;
    left: -10px;
  }

  @media (max-width: 768px) {
    .about-detail-container {
      padding: 10px;
    }

    .container {
      margin: 0;
      margin-top: 10px;
      margin-bottom: 10px;
      padding: 25px 20px;
      border-radius: 8px;
      max-width: none;
      width: 100%;
    }

    h1 {
      font-size: 1.8rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    h2 {
      font-size: 1.4rem;
    }

    h3 {
      font-size: 1.2rem;
    }

    .intro-section {
      padding: 20px;
    }

    .feature-section {
      padding: 15px;
    }
  }

  @media (max-width: 480px) {
    .about-detail-container {
      padding: 5px;
    }

    .container {
      margin: 0;
      padding: 20px 15px;
      border-radius: 6px;
    }

    h1 {
      font-size: 1.6rem;
    }

    .subtitle {
      font-size: 0.9rem;
    }

    h2 {
      font-size: 1.3rem;
    }

    h3 {
      font-size: 1.1rem;
    }

    p {
      font-size: 0.9rem;
    }

    .intro-section {
      padding: 15px;
    }

    .feature-section {
      padding: 12px;
    }

    .use-cases {
      padding: 15px 20px;
    }
  }
</style>
