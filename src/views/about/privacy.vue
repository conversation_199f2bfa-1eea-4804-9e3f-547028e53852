<template>
  <div class="privacy-container">
    <div class="container">
      <h1>{{ getTitle() }}</h1>
      <p class="last-updated">{{ getLastUpdated() }}</p>

      <!-- English Content -->
      <div v-if="currentLocale === 'en'">
        <h2>Our Commitment to Your Privacy</h2>
        <a href="https://dev-app.beppl.co/posts/4zXyLE1JZ" target="_blank">Beppl test</a>
        <br />
        <button @click="goToBeppl">Beppl test</button>
        <br />
        <a :href="bepplUrl">Beppl test</a>
        <p
          >Starchex places the utmost importance on the protection of users' personal information.
          The design and development of Starchex's services and products are deeply rooted in the
          value of user privacy, ensuring that users feel safe and secure while forming meaningful
          connections.</p
        >

        <h3>Commitment to Privacy Protection</h3>
        <p
          >Starchex designs all products and services with a consistent emphasis on user privacy
          protection. We collaborate with experts across various fields, including law, security,
          engineering, and product design, to make decisions that prioritize the privacy of our
          users.</p
        >

        <h3>Commitment to Transparency</h3>
        <p
          >As users of many online services ourselves, we understand that inadequate information and
          overly complex terms are common issues in privacy policies. Therefore, unlike other online
          services, Starchex strives to write its privacy policy and related documents in
          easy-to-understand terms.</p
        >

        <h2>1. Our Information</h2>
        <p>Hyperspirit Inc.<br />26, Apgujeong-ro 8-gil, Gangnam-gu, Seoul, Republic of Korea</p>

        <h2>2. When This Privacy Policy Applies</h2>
        <p
          >This privacy policy applies to all websites, apps, events, and other services operated
          under the Starchex brand name. For ease of understanding, these are collectively referred
          to as 'services' in this privacy policy.</p
        >

        <h2>3. Information Collection</h2>
        <p
          >Without information about users such as basic profile details or preferred partner types,
          it is challenging to recommend meaningful matches. Additionally, Starchex collects
          information about your use of Starchex services.</p
        >

        <h3>Information Provided by Users</h3>
        <p>When using Starchex services, users may provide certain information, such as:</p>
        <ul>
          <li>Essential details like phone number and email address when creating an account</li>
          <li
            >Basic information necessary for the service to operate properly, such as gender, date
            of birth</li
          >
          <li
            >Optional information like sexual orientation, self-introductions, interests, photos,
            and videos</li
          >
          <li
            >Purchasing information and finance details when utilizing paid subscription
            services</li
          >
          <li
            >Opinions, survey responses, and reviews when participating in surveys or market
            research</li
          >
          <li>Information provided when participating in promotions, events, or contests</li>
          <li>Information collected during customer service inquiries</li>
        </ul>

        <h2>4. How Information is Used</h2>
        <p
          >Starchex primarily uses user information to provide and improve services, ensure user
          safety, and offer relevant advertisements.</p
        >
        <ul>
          <li>Creating and managing accounts</li>
          <li>Providing customer support and handling requests</li>
          <li>Recommending users to each other</li>
          <li>Displaying member profiles to one another</li>
          <li>Improving services and developing new services</li>
          <li>Preventing, detecting, and resolving fraud and unauthorized activities</li>
        </ul>

        <h2>5. Information Sharing</h2>
        <p
          >Starchex does not sell user information without consent and only shares it in the
          following cases:</p
        >
        <ul>
          <li>With user consent</li>
          <li>With third-party providers for service operation</li>
          <li>When required by law</li>
          <li>During business transfers</li>
        </ul>

        <h2>6. Data Security</h2>
        <p
          >We implement reasonable security measures to protect information, but complete security
          on the internet cannot be guaranteed.</p
        >

        <h2>7. Data Retention</h2>
        <p
          >We retain personal information only as long as necessary to fulfill the purposes outlined
          in this policy, unless a longer retention period is required by law.</p
        >

        <h2>8. Your Rights</h2>
        <ul>
          <li>Access and modify personal information</li>
          <li>Request account deletion</li>
          <li>Withdraw consent</li>
          <li>File complaints with data protection authorities</li>
        </ul>

        <h2>9. Cookies and Similar Technologies</h2>
        <p>We use cookies to analyze usage behavior, which can be disabled in your browser.</p>

        <h2>10. Changes to This Privacy Policy</h2>
        <p
          >This policy may be changed with prior notice, and continued use of the service
          constitutes agreement to the changes.</p
        >

        <h2>11. Contact</h2>
        <div class="contact-info">
          <p
            ><strong>For questions related to this privacy policy, please contact us at:</strong></p
          >
          <p>Email: <EMAIL></p>
          <p
            >Address: Hyperspirit Inc., 26, Apgujeong-ro 8-gil, Gangnam-gu, Seoul, Republic of
            Korea</p
          >
        </div>
      </div>

      <!-- Korean Content -->
      <div v-else-if="currentLocale === 'ko'">
        <h2>1. 수집하는 정보</h2>
        <ul>
          <li>계정 정보: 이름, 이메일 주소, 전화번호, 생년월일 등</li>
          <li>사용 정보: 접속한 페이지, 클릭한 콘텐츠, 사용 시간 등</li>
          <li>기기 정보: IP 주소, 브라우저 유형, 운영체제 등</li>
          <li>위치 정보: 위치 기반 기능 사용 시 수집됨</li>
          <li>통신 내용: 다른 사용자와의 채팅, 고객센터 요청 등</li>
        </ul>

        <h2>2. 정보 이용 목적</h2>
        <ul>
          <li>Starchex 서비스 제공 및 유지</li>
          <li>사용자 경험 개선 및 신규 기능 개발</li>
          <li>알림 및 고객 지원 제공</li>
          <li>법적 의무 준수</li>
          <li>사기 및 불법 행위 방지</li>
        </ul>

        <h2>3. 정보 공유</h2>
        <p>Starchex는 사용자의 동의 없이 정보를 판매하지 않으며, 아래의 경우에만 공유됩니다.</p>
        <ul>
          <li>사용자 동의</li>
          <li>서비스 운영을 위한 제3자 제공자</li>
          <li>법률상의 요구 사항</li>
          <li>사업 양도 시</li>
        </ul>

        <h2>4. 정보 보관 및 보호</h2>
        <p
          >정보 보호를 위해 합리적인 보안 조치를 시행하고 있으나, 인터넷 상의 완전한 보안은 보장되지
          않습니다.</p
        >

        <h2>5. 사용자의 권리</h2>
        <ul>
          <li>개인정보 열람 및 수정</li>
          <li>계정 삭제 요청</li>
          <li>동의 철회</li>
          <li>데이터 보호 기관에 민원 제기</li>
        </ul>

        <h2>6. 쿠키 및 유사 기술</h2>
        <p>쿠키를 사용하여 사용 행태를 분석하며, 브라우저에서 비활성화할 수 있습니다.</p>

        <h2>7. 아동의 개인정보</h2>
        <p>14세 미만 아동의 서비스 이용은 금지되며, 개인정보 수집 시 즉시 삭제합니다.</p>

        <h2>8. 제3자 링크</h2>
        <p
          >외부 링크는 당사의 개인정보 처리방침과 다를 수 있습니다. 개별 방침을 확인해 주시기
          바랍니다.</p
        >

        <h2>9. 정책 변경</h2>
        <p>본 방침은 사전 공지 후 변경될 수 있으며, 서비스 이용 시 동의한 것으로 간주됩니다.</p>

        <h2>10. 문의</h2>
        <div class="contact-info">
          <p><strong>개인정보 처리방침 관련 문의:</strong></p>
          <p>이메일: <EMAIL></p>
          <p>주소: 대한민국 서울특별시 강남구 Starchex 본사</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { useLocale } from '/@/locales/useLocale'

  const route = useRoute()
  const { changeLocale } = useLocale()

  // Get current locale from route or meta
  const currentLocale = computed(() => {
    const routeLocale = route.meta?.locale as string
    if (routeLocale) {
      return routeLocale
    }

    // Extract locale from path if available
    const pathSegments = route.path.split('/')
    if (pathSegments.length >= 3 && (pathSegments[2] === 'en' || pathSegments[2] === 'ko')) {
      return pathSegments[2]
    }

    return 'en' // default to English
  })

  // Get localized text
  const getTitle = () => {
    return currentLocale.value === 'ko' ? '개인정보 처리방침' : 'Privacy Policy'
  }

  const getLastUpdated = () => {
    return currentLocale.value === 'ko'
      ? '최종 업데이트: 2024년 1월 1일'
      : 'Last updated: January 1, 2024'
  }

  // Set locale when component mounts
  onMounted(async () => {
    const locale = currentLocale.value
    if (locale && ['en', 'ko', 'vi'].includes(locale)) {
      await changeLocale(locale as any)
    }
  })

  const goToBeppl = () => {
    window.location.href = 'https://dev-app.beppl.co/posts/4zXyLE1JZ'
  }

  const bepplUrl = computed(() => {
    return `https://dev-app.beppl.co/posts/4zXyLE1JZ`
  })
</script>

<style scoped>
  .privacy-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
  }

  .container {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: 40px 20px;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin-top: 40px;
    margin-bottom: 40px;
    box-sizing: border-box;
  }

  h1 {
    color: #1890ff;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2.5rem;
    font-weight: 600;
  }

  h2 {
    color: #262626;
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 1.5rem;
    font-weight: 500;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 5px;
  }

  h3 {
    color: #595959;
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 1.2rem;
  }

  p {
    margin-bottom: 15px;
    text-align: left;
  }

  ul {
    margin-bottom: 15px;
    padding-left: 20px;
  }

  li {
    margin-bottom: 8px;
  }

  .last-updated {
    text-align: center;
    color: #8c8c8c;
    font-style: italic;
    margin-bottom: 30px;
  }

  .contact-info {
    background-color: #f0f9ff;
    padding: 20px;
    border-radius: 6px;
    border-left: 4px solid #1890ff;
    margin-top: 30px;
  }

  @media (max-width: 768px) {
    .privacy-container {
      padding: 10px;
    }

    .container {
      margin: 0;
      margin-top: 10px;
      margin-bottom: 10px;
      padding: 25px 20px;
      border-radius: 6px;
      max-width: none;
      width: 100%;
    }

    h1 {
      font-size: 2rem;
    }

    h2 {
      font-size: 1.3rem;
    }

    h3 {
      font-size: 1.1rem;
    }
  }

  @media (max-width: 480px) {
    .privacy-container {
      padding: 5px;
    }

    .container {
      margin: 0;
      padding: 20px 15px;
      border-radius: 4px;
    }

    h1 {
      font-size: 1.8rem;
    }

    h2 {
      font-size: 1.2rem;
    }

    h3 {
      font-size: 1rem;
    }

    p {
      font-size: 0.9rem;
    }

    .contact-info {
      padding: 15px;
    }
  }
</style>
