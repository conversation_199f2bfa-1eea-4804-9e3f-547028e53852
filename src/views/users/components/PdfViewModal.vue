<template>
  <BasicModal
    v-bind="$attrs"
    title="Pdf View"
    @register="register"
    :width="800"
    okText="Download"
    @ok="onOk"
    :canFullscreen="false"
    :loading="isLoading"
  >
    <div class="w-full h-3xl overflow-y-auto" v-if="!isLoading && pages">
      <div v-for="page in pages" :key="page" class="mb-4">
        <VuePDF :pdf="pdf" :page="page" fit-parent />
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { watch } from 'vue'
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { VuePDF, usePDF } from '@tato30/vue-pdf'
  import '@tato30/vue-pdf/style.css'
  import { ref } from 'vue'

  const isLoading = ref(true)
  const props = defineProps<{ pdfUrl: string }>()

  const curPdfUrl = ref(props.pdfUrl)

  watch(
    () => props.pdfUrl,
    (newVal) => {
      isLoading.value = true
      curPdfUrl.value = newVal
    },
  )
  const onProgress = ({ loaded, total }) => {
    isLoading.value = (loaded / total) * 100 < 100
  }

  const { pages, pdf } = usePDF(curPdfUrl, {
    onProgress,
  })

  const [register] = useModalInner((data) => {
    data
  })
  const onOk = () => {
    window.open(props.pdfUrl, '__blank')
  }
</script>
