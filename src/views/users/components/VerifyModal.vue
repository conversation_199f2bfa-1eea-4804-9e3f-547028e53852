<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :title="`${t('common.user.financial.title')} - ${receivedData?.title}`"
    @ok="onClickOk"
    :ok-button-props="{ disabled: disabled }"
    :ok-text="t('common.saveText')"
    :scrollTop="false"
  >
    <div class="financial-modal-content">
      <div class="section">
        <h1 class="section-title">{{ t('common.user.financial.title') }}:</h1>
        <a-radio-group
          v-model:value="selectedFinancialStatus"
          @change="handleFinancialStatusChange"
          class="radio-group"
        >
          <a-radio
            v-for="item in financialStatusOptions"
            :key="item.value"
            :value="item.value"
            class="radio-item"
          >
            <span :class="['status-label']" :style="{ color: item.color }">{{ item.label }}</span>
          </a-radio>
        </a-radio-group>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { useMessage } from '/@/hooks/web/useMessage'
  import { useI18n } from '/@/hooks/web/useI18n'

  type propsData = {
    id?: number
    title?: string
    financialStatus?: string
  }

  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { updateUserFinancialStatus } from '/@/api/users/users'

  const { t } = useI18n()
  const { createMessage } = useMessage()
  const { success, error } = createMessage

  const emit = defineEmits(['finished', 'register'])
  const selectedFinancialStatus = ref<string>()

  const financialStatusOptions = computed(() => [
    /* {
      label: t('common.user.financial.notAuthorized'),
      value: 'doNotAuthorize',
      color: '#d2d2d2',
    },
    {
      label: t('common.user.financial.submitted'),
      value: 'submitted',
      color: '#faad14',
    }, */
    {
      label: t('common.user.financial.risingStar'),
      value: 'risingStar',
      color: '#87d068',
    },
    {
      label: t('common.user.financial.oneStar'),
      value: 'oneStar',
      color: '#089FFA',
    },
    {
      label: t('common.user.financial.twoStar'),
      value: 'twoStar',
      color: '#BF05FA',
    },
    {
      label: t('common.user.financial.threeStar'),
      value: 'threeStar',
      color: '#ff7a45',
    },
    {
      label: t('common.user.financial.fourStar'),
      value: 'fourStar',
      color: '#FC9E05',
    },
    {
      label: t('common.user.financial.fiveStar'),
      value: 'fiveStar',
      color: '#f50',
    },
    {
      label: t('common.user.financial.sixStar'),
      value: 'sixStar',
      color: '#722ed1',
    },
    {
      label: t('common.user.financial.sevenStar'),
      value: 'sevenStar',
      color: '#eb2f96',
    },
  ])

  const disabled = computed(() => {
    return !selectedFinancialStatus.value
  })

  const handleFinancialStatusChange = () => {
    // 可以添加状态变化时的逻辑
  }

  const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
    data && onDataReceive(data)
  })

  const receivedData = ref<propsData>()

  const onDataReceive = (data: propsData) => {
    console.log('onDataReceive', data)
    receivedData.value = data
    selectedFinancialStatus.value = data.financialStatus || 'doNotAuthorize'
  }

  const onClickOk = async () => {
    try {
      changeOkLoading(true)

      const res = await updateUserFinancialStatus(
        receivedData.value!.id!,
        selectedFinancialStatus.value!,
      )

      if (res.ok) {
        success(t('common.user.financial.updateSuccess'))
        emit('finished', {
          financialStatus: res.user.financialStatus,
          level: res.user.level,
          userId: res.user.id,
        })
        closeModal()
      } else {
        error(t('common.user.financial.updateError'))
      }
    } catch (err) {
      error(t('common.user.financial.updateErrorOccurred'))
      console.error('Error updating financial status:', err)
    } finally {
      changeOkLoading(false)
    }
  }
</script>

<style scoped>
  .financial-modal-content {
    padding: 0px;
    height: auto;
    overflow: visible;
  }

  .section {
    margin-bottom: 24px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
  }

  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .radio-item {
    transition: transform 0.2s;
    padding: 12px 16px;
    border-radius: 6px;
    background: white;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    min-height: 48px;
  }

  .radio-item:hover {
    transform: translateX(2px);
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .radio-item :deep(.ant-radio) {
    display: flex;
    align-items: center;
  }

  .radio-item :deep(.ant-radio-wrapper) {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    line-height: 1.4;
  }

  .status-label {
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    min-width: 180px;
    font-size: 14px;
    line-height: 1.4;
    margin-left: 8px;
  }
</style>
