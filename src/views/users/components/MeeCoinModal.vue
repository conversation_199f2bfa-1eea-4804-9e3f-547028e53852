<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    title="Balance recharge/deduction"
    @ok="onClickOk"
  >
    <div class="pt-3px pr-3px">
      <a-form :label-col="labelCol">
        <a-row>
          <a-col :span="24">
            <a-form-item label="User Info:">
              <span>{{ receivedData?.title }} (ID:{{ receivedData?.id }})</span>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="Balance:">
              <span style="color: red">{{ receivedData?.balance ?? '0' }}</span>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="Operation:">
              <a-input-number v-model:value="amount">
                <template #addonBefore>
                  <a-select v-model:value="curOperation" style="width: 60px">
                    <a-select-option
                      v-for="item in operationTypes"
                      :key="item.value"
                      :value="item.value"
                      >{{ item.label }}</a-select-option
                    >
                  </a-select>
                </template>
              </a-input-number>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label=" Note:">
              <a-textarea v-model:value="note" :auto-size="{ minRows: 2, maxRows: 6 }" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue'
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { operationBalance } from '/@/api/users/users'
  import { MeeCoinOperation } from '/@/api/users/model/userModel'
  type Operation = { label: string; value: MeeCoinOperation }
  type propsData = {
    id: number
    title?: string
    balance?: number
  }
  const labelCol = { style: { width: '90px' } }

  const emit = defineEmits(['finished', 'register'])
  const amount = ref<number>()
  const operationTypes = ref<Operation[]>([
    { label: '+', value: MeeCoinOperation.add },
    { label: '-', value: MeeCoinOperation.deduct },
  ])
  const curOperation = ref<Operation>(operationTypes.value[0])
  const note = ref('')

  const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
    data && onDataReceive(data)
  })
  const receivedData = ref<propsData>()
  const onDataReceive = (data: propsData) => {
    console.log('onDataReceive', data)
    receivedData.value = data
  }
  const onClickOk = async () => {
    changeOkLoading(true)
    try {
      const res = await operationBalance(
        curOperation.value!.value,
        receivedData.value!.id,
        Number(amount.value),
        note.value,
      )
      changeOkLoading(false)
      amount.value = 0
      closeModal()
      emit('finished', {
        balance: res.balance,
        id: receivedData.value?.id,
      })
    } catch (error) {
      changeOkLoading(false)
    }
  }
</script>
