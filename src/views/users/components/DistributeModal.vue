<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    title="Recharge"
    :ok-button-props="{ disabled: disabled }"
    ok-text="Recharge"
    @cancel="onReset"
    @ok="onSubmit"
  >
    <Card :bordered="false">
      <a-form :label-col="labelCol" :model="formState">
        <a-form-item label="Type:">
          <a-radio-group v-model:value="formState.audienceType">
            <a-radio :style="radioStyle" value="all">All Users</a-radio>
            <a-radio :style="radioStyle" value="segment">Particular Users </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="formState.audienceType === 'segment'" label="Audiences: ">
          <a-select
            v-model:value="formState.audiences"
            mode="multiple"
            label-in-value
            placeholder="search by username"
            :filter-option="false"
            :not-found-content="searchState.fetching ? undefined : null"
            :options="searchState.data"
            @search="fetchUser"
          >
            <template v-if="searchState.fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
          </a-select>
        </a-form-item>
        <a-form-item label="Amount:">
          <a-input-number
            id="inputNumber"
            v-model:value="formState.amount"
            placeholder="Please enter the amount."
            :min="1"
            :max="9999999"
          />
        </a-form-item>
        <a-form-item label="Note:" :wrapper-col="{ span: 24 }">
          <a-textarea
            v-model:value="formState.note"
            placeholder="please enter the note. This note will be sent to the user."
            :auto-size="{ minRows: 4, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
    </Card>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { Card, message } from 'ant-design-vue'
  import { reactive, ref, watch } from 'vue'
  import { debounce } from 'lodash-es'
  import { getUsers } from '/@/api/users/users'
  import { distributeBalance } from '/@/api/users/users'
  import { SearchParams } from '/@/api/users/model/userModel'
  const emit = defineEmits(['finished', 'register'])

  const [register, { closeModal, changeOkLoading }] = useModalInner((data) => {
    data && onDataReceive(data)
  })
  const onDataReceive = (data) => {
    formState.audienceType = data.audienceType
    const users = data.selectedUsers.map((user) => ({
      key: user.id,
      value: user.id,
      label: user.username,
    }))
    formState.audiences = users
    searchState.data = users
  }

  const labelCol = { style: { width: '80px' } }

  const formState = reactive<any>({
    audienceType: 'all',
    audiences: [],
    amount: '',
    note: '',
  })

  const disabled = ref(true)
  const isLoading = ref(false)

  const radioStyle = reactive({})

  const searchState = reactive<{ data: { label: string; value: number }[]; fetching: boolean }>({
    data: [],
    fetching: false,
  })

  const fetchUser = debounce(async (value) => {
    searchState.data = []
    searchState.fetching = true

    const params: SearchParams = {
      page: 1,
      pageSize: 10,
      username: value,
    }
    try {
      const res = await getUsers(params)
      const data = res.results.map((user) => ({
        label: user.username,
        value: user.id,
      }))
      searchState.data = data
      searchState.fetching = false
    } catch (error) {}
  }, 300)

  watch(formState.audiences, () => {
    searchState.data = []
    searchState.fetching = false
  })

  watch(formState, () => {
    disabled.value =
      !formState.amount ||
      (formState.audienceType === 'segment' && formState.audiences.length === 0)
  })

  const onSubmit = async () => {
    changeOkLoading(true)
    isLoading.value = true
    let audiences = []
    if (formState.audienceType === 'segment') {
      audiences = formState.audiences.map((el: { key: any; label: any }) => {
        return Number(el.key)
      })
    }
    try {
      await distributeBalance(formState.audienceType, audiences, formState.amount, formState.note)
      isLoading.value = false
      message.success(`Recharge Successfully`)
      changeOkLoading(false)
      onReset()
      closeModal()
      emit('finished', { source: 'recharge' })
    } catch (error) {
      isLoading.value = false
      message.error(`Recharge Filed.`)
    }
  }

  const onReset = async () => {
    formState.audiences = []
    formState.audienceType = 'all'
    formState.amount = ''
    formState.note = ''
  }
</script>
