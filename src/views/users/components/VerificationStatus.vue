<template>
  <a-tag :color="color" class="cursor-pointer" @click="handleClick">{{ status }}</a-tag>
</template>
<script lang="ts" setup>
  import { computed } from 'vue'
  import { VerificationStatus } from '/@/api/users/model/userModel'

  const props = defineProps({
    status: {
      type: String as PropType<VerificationStatus>,
      required: true,
    },
  })

  const emit = defineEmits(['click'])

  const handleClick = (e: Event) => {
    emit('click', e)
  }

  const color = computed(() => {
    switch (props.status) {
      case VerificationStatus.incomplete:
        return 'default'
      case VerificationStatus.pending:
        return 'processing'
      case VerificationStatus.notVerifiedYet:
        return 'processing'
      case VerificationStatus.verified:
        return 'success'
      case VerificationStatus.rejected:
        return 'error'
      default:
        return 'default'
    }
  })
</script>

<style scoped>
  .cursor-pointer {
    cursor: pointer;
  }
</style>
