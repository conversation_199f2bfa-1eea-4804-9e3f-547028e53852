<template>
  <PageWrapper title="Users" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="username" label="Username">
                <a-input v-model:value="formState.username" placeholder="username" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="level" label="Level">
                <a-select v-model:value="formState.level" :allowClear="true">
                  <a-select-option
                    v-for="item in levelTypes"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.shortLabel }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="gender" label="Gender">
                <a-select v-model:value="formState.gender" :allowClear="true">
                  <a-select-option value="F" key="F">Female</a-select-option>
                  <a-select-option value="M" key="M">Male</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="verificationType" label="VerificationType">
                <a-select v-model:value="formState.verificationType" :allowClear="true">
                  <a-select-option
                    v-for="item in verificationTypes"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-item>
            </a-col>
            <!-- <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="university" label="University">
                <a-select
                  v-model:value="formState.university"
                  show-search
                  :allowClear="true"
                  :filter-option="filterOption"
                  :options="universities"
                />
              </a-form-item>
            </a-col> -->
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="lastAccessAt" label="Latest Access Time">
                <a-date-picker show-time placeholder="Select Time" @change="onDateChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="mb-4 p-4 bg-white">
        <!-- <div style="margin-bottom: 16px">
          <a-space wrap>
            <a-button type="primary" @click="onRecharge"> Recharge </a-button>

            <a-button type="primary" @click="onMessage"> Message </a-button>

            <a-button type="primary" @click="onExport"> Export </a-button>

            <span style="margin-left: 8px">
              {{
                hasSelected
                  ? `Selected ${state.selectedRowIds.length} users`
                  : `Total ${pagination.total} users`
              }}
            </span>
          </a-space>
        </div> -->
        <a-table
          :row-selection="{ selectedRowKeys: state.selectedRowIds, onChange: onSelectChange }"
          :dataSource="dataSource"
          :pagination="pagination"
          @change="onChange"
          :loading="isLoading"
          size="small"
          :scroll="{ x: 2800 }"
          :rowKey="(record) => record.id"
        >
          <a-table-column key="photos" title="Photos" data-index="photo" :width="80" fixed="left">
            <template #customRender="{ record }">
              <div
                class="table-image mx-auto transform text-center hover:scale-110 duration-300 ease-in-out overflow-hidden w-50px h-50px"
              >
                <a-image
                  :width="50"
                  :height="50"
                  :src="record.fakeAvatar?.formats?.webp?.url || record.fakeAvatar?.url"
                />
              </div>
            </template>
          </a-table-column>
          <!-- <a-table-column key="username" title="Username" data-index="username" :width="140">
            <template #customRender="{ record }">
              <a-tooltip
                placement="topLeft"
                :title="`Visibility: ${record.visibility ? 'True' : 'False'};
                UserName: ${record.username}`"
              >
                <a-space direction="horizontal">
                  <a-badge :status="record.visibility ? 'green' : 'red'" />
                  <a-typography-paragraph>{{
                    record.username.length > 10
                      ? record.username.slice(0, 12) + '...'
                      : record.username
                  }}</a-typography-paragraph>
                </a-space>
              </a-tooltip>
            </template>
          </a-table-column> -->
          <a-table-column key="username" title="Username" data-index="username" :width="140" />
          <a-table-column key="Email" title="Email" data-index="email" :width="220">
            <template #customRender="{ record }">
              <a-tooltip placement="topLeft" :title="record.email">
                {{ record.email }}
              </a-tooltip>
            </template>
          </a-table-column>
          <a-table-column key="deleted" title="User Status" data-index="deleted" :width="100">
            <template #customRender="{ record }">
              <div>
                <a-tag :color="record.deleted ? 'error' : 'success'">
                  {{ record.deleted ? 'delete' : 'active' }}</a-tag
                >
              </div>
            </template>
          </a-table-column>

          <a-table-column key="gender" title="Gender" data-index="gender" :width="80">
            <template #customRender="{ record }">
              {{ record.gender ?? '-' }}
            </template>
          </a-table-column>
          <a-table-column key="fullname" title="Name" data-index="fullname" :width="120">
            <template #customRender="{ record }">
              {{ record.fullname ?? '-' }}
            </template>
          </a-table-column>
          <a-table-column key="title" title="Title" data-index="title" :width="100">
            <template #customRender="{ record }">
              {{ record.title ?? '-' }}
            </template>
          </a-table-column>
          <a-table-column
            key="financialStatus"
            title="Financial Status"
            data-index="financialStatus"
            :width="130"
          >
            <template #customRender="{ record }">
              <a-tag :color="getFinancialColor(record.financialStatus)" class="financial-tag">
                {{ getFinancialText(record.financialStatus) }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column key="idcard" title="ID Card" data-index="idcard" :width="100">
            <template #customRender="{ record }">
              <div v-if="record.idcard" class="idcard-preview">
                <a-image
                  :width="60"
                  :height="38"
                  :src="record.idcard.formats?.webp?.url || record.idcard.url"
                  class="idcard-thumb"
                />
              </div>
              <span v-else class="no-idcard">-</span>
            </template>
          </a-table-column>
          <!-- <a-table-column key="university" title="University" data-index="university" :width="200">
            <template #customRender="{ text }">
              {{ text?.name ?? '-' }}
            </template>
          </a-table-column> -->
          <!-- <a-table-column key="balance" title="Balance" data-index="balance">
            <template #customRender="{ record }">
              <div style="text-align: center"
                ><span style="color: red">{{ record.balance ?? '0' }}</span></div
              >
              <div style="text-align: center">
                <a-button
                  class="ml-2 inline-block"
                  type="primary"
                  @click="onClickMCBtn(record)"
                  size="small"
                  >op</a-button
                >
              </div>
            </template>
          </a-table-column> 
          <a-table-column key="referralCode" title="ReferralCode" data-index="referralCode" />
          <a-table-column key="regRefCode" title="Register RefCode" data-index="regRefCode">
            <template #customRender="{ record }">
              {{ record.regRefCode ?? '-' }}
            </template>
          </a-table-column>-->

          <a-table-column
            key="lastAccessAt"
            title="Latest Access"
            data-index="lastAccessAt"
            :width="140"
          >
            <template #customRender="{ record }">
              {{ formatToDateTime(record.lastAccessAt) }}
            </template>
          </a-table-column>
          <a-table-column key="createdAt" title="Created Time" data-index="createdAt" :width="140">
            <template #customRender="{ record }">
              {{ formatToDateTime(record.createdAt) }}
            </template>
          </a-table-column>
          <a-table-column key="operation" title="Action" fixed="right" :width="150">
            <template #customRender="{ record }">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">Detail</a-button>
                <a-popconfirm
                  :title="t('common.deleteConfirmTitle')"
                  :ok-text="t('common.yesText')"
                  :cancel-text="t('common.noText')"
                  @confirm="handleDeleteUser(record)"
                >
                  <a-button type="link" size="small" danger>{{ t('common.delText') }}</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </a-table-column>
        </a-table>
      </div>
    </div>
    <MeeCoinModal @register="meeCoinModalRegister" @finished="onMeeCoinChanged" />
    <DistributeModal @register="distributeModalRegister" @finished="onModalClosed" />
    <MessageModal @register="messageModalRegister" @finished="onModalClosed" />
  </PageWrapper>
</template>
<script lang="ts" setup name="usersList">
  import { ref, reactive, onMounted, onActivated, computed } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { getUsers, getUniversities, deleteUser } from '/@/api/users/users'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import { useModal } from '/@/components/Modal'
  import { message } from 'ant-design-vue'
  import { useI18n } from '/@/hooks/web/useI18n'
  import MeeCoinModal from './components/MeeCoinModal.vue'
  import DistributeModal from './components/DistributeModal.vue'
  import MessageModal from '../messages/components/MessageModal.vue'

  import '/node_modules/flag-icons/css/flag-icons.min.css'

  import {
    SearchParams,
    UserModel,
    LightUserModel,
    VerificationType,
  } from '/@/api/users/model/userModel'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import dayjs from 'dayjs'
  const [meeCoinModalRegister, { openModal: _openMeeCoinModal }] = useModal()
  const [distributeModalRegister, { openModal: _openDistributeModal }] = useModal()
  const [messageModalRegister, { openModal: _openMessageModal }] = useModal()

  const { t } = useI18n()
  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<UserModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const state = reactive<{
    selectedRowIds: []
    selectedUsers: LightUserModel[]
  }>({
    selectedRowIds: [],
    selectedUsers: [],
  })
  const _hasSelected = computed(() => state.selectedRowIds.length > 0)

  const onSelectChange = (selectedRowKeys) => {
    state.selectedRowIds = selectedRowKeys
    state.selectedUsers = dataSource.value
      .filter((user) => selectedRowKeys.includes(user.id))
      .map(({ id, username }) => ({ id, username }))
  }

  const onChange = (pageChanged) => {
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showDetail = (record: UserModel): void => {
    go({
      name: 'user',
      params: { id: record.id, username: record.username },
    })
  }

  const handleDeleteUser = async (record: UserModel) => {
    try {
      await deleteUser(record.id)
      message.success(t('common.deleteSuccessMessage'))
      // 删除成功后刷新数据
      fetchData()
    } catch (error) {
      console.error('Failed to delete user:', error)
      message.error(t('common.deleteErrorMessage'))
    }
  }

  const financialStatusArr = computed(() => [
    {
      label: t('common.user.financial.notAuthorized'),
      value: 'doNotAuthorize',
      color: '#d2d2d2',
    },
    {
      label: t('common.user.financial.submitted'),
      value: 'submitted',
      color: '#faad14',
    },
    {
      label: t('common.user.financial.risingStar'),
      value: 'risingStar',
      color: '#87d068',
    },
    {
      label: t('common.user.financial.oneStar'),
      value: 'oneStar',
      color: '#089FFA',
    },
    {
      label: t('common.user.financial.twoStar'),
      value: 'twoStar',
      color: '#BF05FA',
    },
    {
      label: t('common.user.financial.threeStar'),
      value: 'threeStar',
      color: '#ff7a45',
    },
    {
      label: t('common.user.financial.fourStar'),
      value: 'fourStar',
      color: '#FC9E05',
    },
    {
      label: t('common.user.financial.fiveStar'),
      value: 'fiveStar',
      color: '#f50',
    },
    {
      label: t('common.user.financial.sixStar'),
      value: 'sixStar',
      color: '#722ed1',
    },
    {
      label: t('common.user.financial.sevenStar'),
      value: 'sevenStar',
      color: '#eb2f96',
    },
  ])

  const getFinancialColor = (value = 'doNotAuthorize') => {
    return financialStatusArr.value.find((el) => el.value == value)?.color
  }

  const getFinancialText = (value = 'doNotAuthorize') => {
    return financialStatusArr.value.find((el) => el.value == value)?.label
  }

  const _getLevelColor = (level) => {
    return levelTypes.value.find((el) => el.value == level)?.color
  }

  const _getLevelText = (level) => {
    return levelTypes.value.find((el) => el.value == level)?.shortLabel
  }

  const _getLongText = (level) => {
    return levelTypes.value.find((el) => el.value == level)?.label
  }

  const _getFlagClass = (regionCode) => {
    if (regionCode) {
      regionCode = regionCode.toLowerCase()
      const className = `fi fi-${regionCode}`
      return className
    }
    return ''
  }

  const formState = reactive({
    username: '',
    university: '',
    verificationType: '',
    startDate: '',
    endDate: '',
    level: '',
    gender: '',
    latestAccessTime: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const onDateChange = (value: Dayjs, dateString: string) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.latestAccessTime = dateString
  }

  const verificationTypes = ref<{ label: string; value: VerificationType }[]>([
    { label: 'Email', value: VerificationType.email },
    { label: 'Certificate', value: VerificationType.certificate },
  ])

  const universities = ref<{ label: string; value: number }[]>()
  // const filterOption = (input: string, option: { label: string; value: number }) => {
  //   return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  // }

  const levelTypes = ref<{ label: string; shortLabel: string; value: number; color: string }[]>([
    { label: 'best, premium card pool', value: 5, shortLabel: 'best', color: '#FC9E05' },
    { label: 'very good, premium card pool', value: 4, shortLabel: 'very good', color: '#BF05FA' },
    {
      label: 'not bad, normal, in given card',
      value: 3,
      shortLabel: 'not bad',
      color: '#089FFA',
    },
    {
      label: 'so so, not his/her actual picture, in given card',
      value: 2,
      shortLabel: 'so so',
      color: '#0BE153',
    },
    {
      label: 'no recommend, picture has problem - do not show in given card',
      value: 1,
      shortLabel: 'no recommend',
      color: '#d2d2d2',
    },
  ])

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchUniversities = async () => {
    const res = await getUniversities()
    universities.value = res.map((item) => ({ label: item.name, value: item.id }))
  }
  fetchUniversities()

  const fetchData = async () => {
    state.selectedRowIds = []
    state.selectedUsers = []

    isLoading.value = true
    const params: SearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      username: formState.username,
      university: formState.university == '' ? null : Number(formState.university),
      verificationType: formState.verificationType,
      startDate: formState.startDate,
      endDate: formState.endDate,
      level: formState.level == '' || formState.level == undefined ? null : Number(formState.level),
      gender: formState.gender,
      latestAccessTime: formState.latestAccessTime
        ? dayjs(formState.latestAccessTime).valueOf().toString()
        : '',
    }

    try {
      const res = await getUsers(params)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      isLoading.value = false
    }
  }
  onMounted(() => {
    // fetchData()
  })

  onActivated(() => {
    fetchData()
  })

  const onMeeCoinChanged = (result) => {
    Object.assign(dataSource.value.filter((item) => result.id === item.id)[0], {
      balance: result.balance,
    })
  }

  const onModalClosed = (source) => {
    if (source === 'recharge') fetchData()
  }

  /* const onClickMCBtn = (record) => {
    openMeeCoinModal(true, {
      id: record.id,
      title: record.username,
      balance: record.balance,
    })
  }

  const onRecharge = () => {
    openDistributeModal(true, {
      audienceType: state.selectedUsers.length == 0 ? 'all' : 'segment',
      selectedUsers: state.selectedUsers,
    })
  }

  const onMessage = () => {
    openMessageModal(true, {
      audienceType: state.selectedUsers.length == 0 ? 'all' : 'segment',
      selectedUsers: state.selectedUsers,
    })
  }

  const onExport = async () => {
    const params: SearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      username: formState.username,
      university: formState.university == '' ? null : Number(formState.university),
      verificationType: formState.verificationType,
      startDate: formState.startDate,
      endDate: formState.endDate,
      level: formState.level == '' || formState.level == undefined ? null : Number(formState.level),
      gender: formState.gender,
      latestAccessTime: formState.latestAccessTime
        ? dayjs(formState.latestAccessTime).valueOf().toString()
        : '',
    }

    try {
      const res = await exportUsers(params)
      downloadByData(res, `MeeTok-Users-${dayjs().format('YYYY-MM-DD HH:mm')}.xlsx`)
    } catch (error) {
      isLoading.value = false
    }
  } */
</script>
<style scoped>
  .table-image :deep(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .financial-tag {
    font-weight: 500;
    font-size: 12px;
  }

  .idcard-preview {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .idcard-thumb {
    border-radius: 4px;
    object-fit: cover;
    border: 1px solid #e8e8e8;
    transition: transform 0.2s ease;
  }

  .idcard-thumb:hover {
    transform: scale(1.05);
  }

  .no-idcard {
    color: #999;
    font-style: italic;
  }
</style>
