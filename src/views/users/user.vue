<template>
  <PageWrapper :title="t('common.user.detail.title', { username: userInfo?.username })" content="">
    <div class="user-detail-container">
      <!-- 用户头部信息 -->
      <a-card class="user-header-section" :bordered="false">
        <div class="user-profile-header">
          <div class="avatar-section">
            <a-avatar
              :size="80"
              :src="userInfo?.photos?.[0]?.formats?.webp?.url || userInfo?.photos?.[0]?.url"
              class="user-avatar"
            >
              <template #icon v-if="!userInfo?.photos?.[0]">
                <user-outlined />
              </template>
            </a-avatar>
            <div class="status-indicator">
              <a-tag :color="userInfo?.deleted ? 'error' : 'success'" size="small">
                {{
                  userInfo?.deleted
                    ? t('common.user.status.deleted')
                    : t('common.user.status.active')
                }}
              </a-tag>
            </div>
          </div>
          <div class="user-info">
            <h2 class="username">{{ userInfo?.username }}</h2>
            <p class="fullname" v-if="userInfo?.fullname">{{ userInfo?.fullname }}</p>
            <div class="meta-info">
              <span v-if="userInfo?.gender" class="meta-item">
                <user-outlined />
                {{ userInfo?.gender }}
              </span>
              <span v-if="userInfo?.birthday" class="meta-item">
                <calendar-outlined />
                {{ userInfo?.birthday }}
              </span>
              <span class="meta-item">
                <clock-circle-outlined />
                {{ formatToDateTime(dayjs(userInfo?.createdAt)) }}
              </span>
            </div>
          </div>
          <div class="action-section">
            <a-button
              type="primary"
              @click="onClickManageFinancialStatus"
              :icon="h(SafetyCertificateOutlined)"
            >
              {{ t('common.user.action.manageFinancialStatus') }}
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- 主要信息区域 -->
      <div class="detail-section">
        <!-- 基本信息 -->
        <a-card :title="t('common.user.section.basicInfo')" class="info-card basic-info-card">
          <a-descriptions :column="2" size="small" :colon="false">
            <a-descriptions-item :label="t('common.user.field.provider')">
              <div class="provider-info">
                <span>{{ userInfo?.provider }}: {{ userInfo?.email }}</span>
                <div
                  class="provider-actions"
                  v-if="userInfo?.provider === 'google' && userInfo?.email"
                >
                  <a-tooltip :title="t('common.user.action.copyEmail')">
                    <a-button type="text" size="small" @click="onClickCopy">
                      <copy-outlined />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip :title="t('common.user.action.sendEmail')">
                    <a :href="`mailto:${userInfo?.email}`">
                      <a-button type="text" size="small">
                        <mail-outlined />
                      </a-button>
                    </a>
                  </a-tooltip>
                </div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item :label="t('common.user.field.phone')" v-if="userInfo?.phone">
              <div class="phone-info">
                <span>{{ `${userInfo?.countryCode ?? '-'} ${userInfo?.phone ?? '-'}` }}</span>
                <a-tag
                  :color="getPhoneVerificationStatusColor(userInfo?.phoneVerificationStatus)"
                  size="small"
                >
                  {{ userInfo?.phoneVerificationStatus || 'pass' }}
                </a-tag>
              </div>
            </a-descriptions-item>
            <a-descriptions-item :label="t('common.user.field.nationality')">
              <div class="nationality-info">
                <span>{{ userInfo?.nationalityCode ?? '-' }}</span>
                <span :class="getFlagClass(userInfo?.nationalityCode)" class="flag-icon"></span>
              </div>
            </a-descriptions-item>
            <a-descriptions-item :label="t('common.user.field.financialStatus')">
              <a-tag :color="getFinancialColor(userInfo?.financialStatus)" class="financial-tag">
                {{ getFinancialText(userInfo?.financialStatus) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item :label="t('common.user.field.level')">
              <div class="level-display">
                <a-rate
                  :value="userInfo?.level"
                  :count="7"
                  @change="handleLevelChange"
                  class="level-stars"
                  disabled
                />
                <span class="level-text">Level {{ userInfo?.level || 0 }}</span>
              </div>
            </a-descriptions-item>
            <a-descriptions-item
              :label="t('common.user.field.idcard')"
              v-if="userInfo?.idcard"
              :span="2"
            >
              <div class="idcard-display-enhanced">
                <div class="idcard-preview">
                  <FilePdfOutlined
                    v-if="isPdfFile(userInfo.idcard.url)"
                    class="pdf-icon-large"
                    @click="onClickPDF(userInfo.idcard.url)"
                  />
                  <a-image
                    v-else
                    :width="120"
                    :height="75"
                    :src="userInfo.idcard.formats?.webp?.url || userInfo.idcard.url"
                    class="idcard-image-large"
                    :preview="{ mask: t('common.user.action.preview') }"
                  />
                </div>
                <div class="idcard-info">
                  <span class="idcard-label">{{ t('common.user.field.idcard') }}</span>
                  <span class="idcard-status">{{ t('common.user.status.uploaded') }}</span>
                </div>
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>

      <!-- 扩展信息 -->
      <div class="extended-info">
        <!-- 兴趣爱好 -->
        <a-card
          :title="t('common.user.section.interests')"
          v-if="userInfo?.interests?.length"
          class="full-width-card"
        >
          <div class="interests-list">
            <a-tag
              color="blue"
              v-for="interest in userInfo?.interests || []"
              :key="interest.id"
              class="interest-item"
            >
              {{ interest.name }}
            </a-tag>
          </div>
        </a-card>

        <!-- 个人照片 -->
        <a-card
          :title="t('common.user.section.photos')"
          v-if="userInfo?.photos?.length"
          class="full-width-card"
        >
          <div class="photos-grid">
            <a-image-preview-group>
              <div v-for="image in userInfo?.photos || []" :key="image.id" class="photo-wrapper">
                <a-image
                  :width="100"
                  :height="100"
                  :src="image.formats?.webp?.url || image.url"
                  class="photo-item"
                  :preview="{ mask: t('common.user.action.preview') }"
                />
              </div>
            </a-image-preview-group>
          </div>
        </a-card>

        <!-- 个人简介 -->
        <a-card
          :title="t('common.user.section.introduction')"
          v-if="userInfo?.introduction"
          class="full-width-card"
        >
          <div class="introduction-content">
            {{ userInfo?.introduction }}
          </div>
        </a-card>

        <!-- 财务状态与资产验证 -->
        <a-card
          :title="t('common.user.financial.title')"
          class="full-width-card financial-status-card"
        >
          <div class="financial-status-content">
            <!-- 财务状态概览 -->
            <div class="financial-overview">
              <div class="status-item">
                <span class="status-label">{{ t('common.user.field.financialStatus') }}:</span>
                <a-tag :color="getFinancialColor(userInfo?.financialStatus)" class="financial-tag">
                  {{ getFinancialText(userInfo?.financialStatus) }}
                </a-tag>
              </div>
              <div v-if="userInfo?.verificationEmail" class="status-item">
                <span class="status-label">{{ t('common.user.field.email') }}:</span>
                <span class="status-value">{{ userInfo.verificationEmail }}</span>
              </div>
            </div>

            <!-- 资产验证详情 -->
            <div v-if="_hasAssetVerifications" class="asset-details">
              <!-- 资产验证 -->
              <div v-if="_allAssetVerifications.length > 0" class="asset-category">
                <div class="category-header">
                  <span class="category-title">{{ t('common.user.field.assetVerification') }}</span>
                  <a-tag size="small" color="blue"
                    >{{ _allAssetVerifications.length }} {{ t('common.user.unit.items') }}</a-tag
                  >
                </div>
                <div class="asset-items">
                  <div
                    v-for="verification in _allAssetVerifications"
                    :key="verification.id"
                    class="asset-item-enhanced"
                  >
                    <div class="asset-header">
                      <div class="asset-title-section">
                        <a-tag
                          :color="_getAssetTypeColor(verification.type)"
                          size="default"
                          class="asset-type-tag"
                        >
                          {{ t(`common.user.assetType.${verification.type}`) }}
                        </a-tag>
                        <span class="asset-name" v-if="verification.name">{{
                          verification.name
                        }}</span>
                      </div>
                      <div class="asset-count">
                        <a-tag size="small" color="geekblue">
                          {{ verification.proof.length }} {{ t('common.user.unit.files') }}
                        </a-tag>
                      </div>
                    </div>
                    <div class="asset-description" v-if="verification.description">
                      {{ verification.description }}
                    </div>
                    <div class="proof-gallery">
                      <a-image-preview-group>
                        <div v-for="proof in verification.proof" :key="proof.id" class="proof-item">
                          <div class="proof-wrapper">
                            <FilePdfOutlined
                              v-if="isPdfFile(proof.url)"
                              class="pdf-icon"
                              @click="onClickPDF(proof.url)"
                            />
                            <a-image
                              v-else
                              :width="80"
                              :height="80"
                              :src="getOptimalImageUrl(proof)"
                              class="proof-image"
                              :preview="{ mask: t('common.user.action.preview') }"
                            />
                          </div>
                          <!-- File info hidden as requested -->
                          <!-- <div class="proof-info">
                            <span class="proof-name">{{ proof.name }}</span>
                            <span class="proof-size">{{ formatFileSize(proof.size) }}</span>
                          </div> -->
                        </div>
                      </a-image-preview-group>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 证书文件 -->
              <div
                v-if="userInfo?.spouse?.length || userInfo?.corporate?.length"
                class="certificates-section"
              >
                <div class="certificates-title">{{ t('common.user.certificate.title') }}</div>
                <div class="certificates-grid">
                  <div v-if="userInfo?.spouse?.length" class="cert-group">
                    <span class="cert-label">{{ t('common.user.certificate.spouse') }}</span>
                    <div class="cert-files">
                      <a-image-preview-group>
                        <div
                          v-for="doc in userInfo.spouse.slice(0, 2)"
                          :key="doc.id"
                          class="cert-thumb"
                        >
                          <FilePdfOutlined
                            v-if="isPdfFile(doc.url)"
                            class="pdf-thumb"
                            @click="onClickPDF(doc.url)"
                          />
                          <a-image
                            v-else
                            :width="24"
                            :height="24"
                            :src="doc.formats?.webp?.url || doc.url"
                            class="cert-image"
                          />
                        </div>
                        <span v-if="userInfo.spouse.length > 2" class="more-count small">
                          +{{ userInfo.spouse.length - 2 }}
                        </span>
                      </a-image-preview-group>
                    </div>
                  </div>
                  <div v-if="userInfo?.corporate?.length" class="cert-group">
                    <span class="cert-label">{{ t('common.user.certificate.corporate') }}</span>
                    <div class="cert-files">
                      <a-image-preview-group>
                        <div
                          v-for="doc in userInfo.corporate.slice(0, 2)"
                          :key="doc.id"
                          class="cert-thumb"
                        >
                          <FilePdfOutlined
                            v-if="isPdfFile(doc.url)"
                            class="pdf-thumb"
                            @click="onClickPDF(doc.url)"
                          />
                          <a-image
                            v-else
                            :width="24"
                            :height="24"
                            :src="doc.formats?.webp?.url || doc.url"
                            class="cert-image"
                          />
                        </div>
                        <span v-if="userInfo.corporate.length > 2" class="more-count small">
                          +{{ userInfo.corporate.length - 2 }}
                        </span>
                      </a-image-preview-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 无资产验证时的提示 -->
            <div v-else class="no-assets">
              <span class="no-assets-text">{{ t('common.user.noAssets') }}</span>
            </div>
          </div>
        </a-card>
      </div>
    </div>

    <VerifyModal @register="verifyModalRegister" @finished="onVerifyChanged" />
    <MeeCoinModal @register="meeCoinModalRegister" @finished="_onMeeCoinChanged" />
    <PdfViewModal @register="pdfViewModalRegister" :pdfUrl="curPdfUrl" v-if="curPdfUrl" />
  </PageWrapper>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router'
  import { getUser } from '/@/api/users/users'
  import { PageWrapper } from '/@/components/Page'
  import { computed, ref, onMounted, nextTick, h } from 'vue'
  import { UserModel, AssetVerificationType } from '/@/api/users/model/userModel'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import dayjs from 'dayjs'
  import VerifyModal from './components/VerifyModal.vue'
  import { useModal } from '/@/components/Modal'
  import MeeCoinModal from './components/MeeCoinModal.vue'
  import { useTabs } from '/@/hooks/web/useTabs'
  import { useI18n } from '/@/hooks/web/useI18n'
  import {
    MailOutlined,
    CopyOutlined,
    FilePdfOutlined,
    UserOutlined,
    SafetyCertificateOutlined,
    CalendarOutlined,
    ClockCircleOutlined,
  } from '@ant-design/icons-vue'
  import { useClipboard } from '@vueuse/core'
  import { useMessage } from '/@/hooks/web/useMessage'
  import { changeLevel } from '/@/api/users/users'
  import '/node_modules/flag-icons/css/flag-icons.min.css'
  import PdfViewModal from './components/PdfViewModal.vue'

  const { t } = useI18n()
  const { createMessage } = useMessage()
  const { success } = createMessage
  const { id, username } = useRoute().params
  const [verifyModalRegister, { openModal: openVerifyModal }] = useModal()
  const [meeCoinModalRegister, { openModal: openMeeCoinModal }] = useModal()
  const [pdfViewModalRegister, { openModal: openPdfViewModal }] = useModal()
  const isLoading = ref(true)
  const userInfo = ref<UserModel>()
  const { setTitle } = useTabs()
  const emailSource = ref(userInfo.value?.email ?? '')
  const { copy } = useClipboard({ source: emailSource })

  const financialStatusArr = computed(() => [
    {
      label: t('common.user.financial.notAuthorized'),
      value: 'doNotAuthorize',
      color: '#d2d2d2',
    },
    {
      label: t('common.user.financial.submitted'),
      value: 'submitted',
      color: '#faad14',
    },
    {
      label: t('common.user.financial.risingStar'),
      value: 'risingStar',
      color: '#87d068',
    },
    {
      label: t('common.user.financial.oneStar'),
      value: 'oneStar',
      color: '#089FFA',
    },
    {
      label: t('common.user.financial.twoStar'),
      value: 'twoStar',
      color: '#BF05FA',
    },
    {
      label: t('common.user.financial.threeStar'),
      value: 'threeStar',
      color: '#ff7a45',
    },
    {
      label: t('common.user.financial.fourStar'),
      value: 'fourStar',
      color: '#FC9E05',
    },
    {
      label: t('common.user.financial.fiveStar'),
      value: 'fiveStar',
      color: '#f50',
    },
    {
      label: t('common.user.financial.sixStar'),
      value: 'sixStar',
      color: '#722ed1',
    },
    {
      label: t('common.user.financial.sevenStar'),
      value: 'sevenStar',
      color: '#eb2f96',
    },
  ])

  const getFinancialColor = (value = 'doNotAuthorize') => {
    return financialStatusArr.value.find((el) => el.value == value)?.color
  }
  const getFinancialText = (value = 'doNotAuthorize') => {
    return financialStatusArr.value.find((el) => el.value == value)?.label
  }

  const getPhoneVerificationStatusColor = (value) => {
    if (value === 'done') {
      return 'success'
    } else if (value === 'no') {
      return '#f50'
    } else {
      return 'cyan'
    }
  }

  setTitle(username as string)
  const fetchData = async () => {
    try {
      const res = await getUser(Number(id))
      console.log(res)
      userInfo.value = res
      setTitle(userInfo.value.username)
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    fetchData()
  })
  const onClickManageFinancialStatus = () => {
    openVerifyModal(true, {
      id: id,
      title: userInfo?.value?.username,
      financialStatus: userInfo?.value?.financialStatus,
    })
  }
  const onVerifyChanged = ({
    financialStatus,
    level,
    userId,
  }: {
    financialStatus?: string
    level?: number
    userId?: number
  }) => {
    if (userInfo.value && userId === userInfo.value.id) {
      // Update the reactive data immediately for instant UI update
      if (financialStatus !== undefined) {
        userInfo.value.financialStatus = financialStatus as any
      }
      if (level !== undefined) {
        userInfo.value.level = level
      }
      // Data is updated immediately - UI will reflect changes instantly
      console.log('Financial status updated:', { financialStatus, level })
    }
  }

  const _onClickMCBtn = () => {
    openMeeCoinModal(true, {
      id: id,
      title: userInfo?.value?.username,
      balance: userInfo?.value?.balance,
    })
  }
  const _onMeeCoinChanged = (result) => {
    if (userInfo.value) {
      userInfo.value.balance = result.balance
    }
  }
  const onClickCopy = () => {
    copy(userInfo.value?.email)
    success(t('common.user.message.emailCopied'))
  }

  const handleLevelChange = async (value: number) => {
    console.log(`selected ${value}`)
    if (!userInfo.value) return

    try {
      const res = await changeLevel(userInfo.value.id, Number(value))
      userInfo.value.level = res.level
      console.log(res)
    } catch (error) {}
  }

  const getFlagClass = (regionCode) => {
    if (regionCode) {
      regionCode = regionCode.toLowerCase()
      const className = `fi fi-${regionCode}`
      return className
    }
    return ''
  }

  // 资产验证相关计算属性
  const _hasAssetVerifications = computed(() => {
    return (
      (userInfo.value?.asset_verifications?.length ?? 0) > 0 ||
      (userInfo.value?.spouse?.length ?? 0) > 0 ||
      (userInfo.value?.corporate?.length ?? 0) > 0 ||
      userInfo.value?.verificationEmail
    )
  })

  const _allAssetVerifications = computed(() => {
    return userInfo.value?.asset_verifications ?? []
  })

  // 工具函数
  const isPdfFile = (url: string) => {
    return url.toLowerCase().endsWith('.pdf')
  }

  const _getAssetTypeColor = (type: AssetVerificationType) => {
    const colorMap = {
      [AssetVerificationType.RealEstate]: 'green',
      [AssetVerificationType.FinancialAssets]: 'blue',
      [AssetVerificationType.CryptoCurrency]: 'orange',
      [AssetVerificationType.Others]: 'purple',
    }
    return colorMap[type] || 'default'
  }

  const curPdfUrl = ref('')
  const onClickPDF = (url) => {
    curPdfUrl.value = url
    nextTick(() => {
      openPdfViewModal()
    })
  }

  // 获取最优图片URL
  const getOptimalImageUrl = (proof) => {
    if (proof.formats?.medium?.url) {
      return proof.formats.medium.url
    }
    if (proof.formats?.small?.url) {
      return proof.formats.small.url
    }
    if (proof.formats?.thumbnail?.url) {
      return proof.formats.thumbnail.url
    }
    return proof.url
  }

  // File size formatting function removed as it's no longer used
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 768px) {
    .user-profile-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .idcard-display-enhanced {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .basic-info-card :deep(.ant-descriptions) {
      grid-template-columns: 1fr !important;
    }

    .proof-gallery {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
    }

    .asset-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .asset-title-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .provider-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .level-display {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .photos-grid {
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      gap: 12px;
    }

    .asset-verification-container {
      gap: 16px;
    }

    .asset-group {
      padding: 12px;
    }

    .asset-list {
      gap: 12px;
    }

    .asset-item {
      padding: 8px;
    }

    .asset-proof-list {
      gap: 6px;
    }

    .spouse-documents,
    .corporate-documents {
      gap: 8px;
    }
  }

  .user-detail-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }

  .user-header-section {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .user-profile-header {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  .avatar-section {
    position: relative;
    flex-shrink: 0;
  }

  .user-avatar {
    border: 3px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .status-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
  }

  .user-info {
    flex: 1;
    min-width: 0;
  }

  .username {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #262626;
  }

  .fullname {
    font-size: 16px;
    color: #595959;
    margin: 0 0 12px 0;
  }

  .meta-info {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #8c8c8c;
    font-size: 14px;
  }

  .action-section {
    flex-shrink: 0;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .basic-info-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .info-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .info-card :deep(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
  }

  .info-card :deep(.ant-card-head-title) {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .info-card :deep(.ant-card-body) {
    padding: 16px;
  }

  .provider-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
  }

  .provider-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
  }

  .phone-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .nationality-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .flag-icon {
    width: 20px;
    height: 15px;
  }

  .financial-tag {
    font-weight: 500;
  }

  .level-display {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .level-stars {
    font-size: 16px;
  }

  .level-text {
    background: linear-gradient(135deg, #1890ff, #096dd9);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  .idcard-display {
    display: flex;
    align-items: center;
  }

  .idcard-image {
    border-radius: 4px;
    object-fit: cover;
    border: 1px solid #e8e8e8;
    transition: transform 0.2s ease;
  }

  .idcard-image:hover {
    transform: scale(1.05);
  }

  .idcard-display-enhanced {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    overflow: hidden;
  }

  .idcard-preview {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 140px;
    max-height: 90px;
    overflow: hidden;
  }

  .idcard-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .idcard-label {
    font-size: 13px;
    font-weight: 600;
    color: #262626;
  }

  .idcard-status {
    font-size: 12px;
    color: #52c41a;
    font-weight: 500;
  }

  .idcard-image-large {
    border-radius: 6px;
    object-fit: cover;
    border: 1px solid #d9d9d9;
    transition: transform 0.2s ease;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
  }

  .idcard-image-large:hover {
    transform: scale(1.02);
  }

  .pdf-icon-large {
    font-size: 32px;
    color: #ff4d4f;
    cursor: pointer;
    padding: 20px;
    background: #fff2f0;
    border-radius: 6px;
    border: 1px solid #ffccc7;
    transition: all 0.2s ease;
    max-width: 100%;
    max-height: 100%;
  }

  .pdf-icon-large:hover {
    color: #ff7875;
    background: #fff1f0;
  }

  .verification-status {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .verification-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .email-verification {
    font-size: 14px;
    color: #595959;
  }

  .certificate-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .certificate-item {
    transition: transform 0.2s ease;
  }

  .certificate-item:hover {
    transform: scale(1.05);
  }

  .pdf-icon {
    font-size: 24px;
    color: #ff4d4f;
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .pdf-icon:hover {
    color: #ff7875;
  }

  .certificate-image {
    border-radius: 4px;
    object-fit: cover;
  }

  .extended-info {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .full-width-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .full-width-card :deep(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
  }

  .full-width-card :deep(.ant-card-head-title) {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .full-width-card :deep(.ant-card-body) {
    padding: 16px;
  }

  .interests-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .interest-item {
    font-size: 13px;
    padding: 4px 12px;
    border-radius: 12px;
  }

  .photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 16px;
    max-width: 100%;
    overflow: hidden;
  }

  .photo-wrapper {
    transition: transform 0.2s ease;
    width: 100px;
    height: 100px;
    overflow: hidden;
    border-radius: 8px;
  }

  .photo-wrapper:hover {
    transform: scale(1.02);
  }

  .photo-item {
    border-radius: 8px;
    object-fit: cover;
    width: 100% !important;
    height: 100% !important;
  }

  .introduction-content {
    line-height: 1.6;
    color: #595959;
    font-size: 14px;
    white-space: pre-wrap;
  }

  /* 财务状态卡片样式 */
  .financial-status-card {
    min-height: 200px;
  }

  .financial-status-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  /* 财务状态概览 */
  .financial-overview {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1890ff;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
  }

  .status-label {
    font-weight: 500;
    color: #595959;
    min-width: 80px;
  }

  .status-value {
    color: #262626;
  }

  .graduate-status {
    margin-left: 4px;
  }

  /* 资产验证详情 */
  .asset-details {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .asset-category {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
  }

  .category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
  }

  .category-title {
    font-size: 13px;
    font-weight: 600;
    color: #262626;
  }

  .asset-items {
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .asset-item-compact {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: white;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .asset-item-compact:hover {
    border-color: #d9d9d9;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }

  .asset-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    margin-right: 12px;
  }

  .asset-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .asset-meta .asset-name {
    font-size: 12px;
    font-weight: 600;
    color: #262626;
  }

  .asset-meta .asset-description {
    font-size: 11px;
    color: #8c8c8c;
    line-height: 1.3;
  }

  .asset-type {
    font-size: 11px;
    font-weight: 500;
  }

  /* 增强的资产验证样式 */
  .asset-item-enhanced {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
  }

  .asset-item-enhanced:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
  }

  .asset-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .asset-title-section {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .asset-type-tag {
    font-weight: 600;
  }

  .asset-name {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .asset-count {
    flex-shrink: 0;
  }

  .asset-description {
    color: #8c8c8c;
    font-size: 14px;
    margin-bottom: 16px;
    line-height: 1.5;
  }

  .proof-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
  }

  .proof-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }

  .proof-wrapper {
    width: 80px;
    height: 80px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .proof-wrapper:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }

  .proof-image {
    border-radius: 4px;
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  .proof-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
  }

  .proof-name {
    font-size: 12px;
    color: #262626;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .proof-size {
    font-size: 11px;
    color: #8c8c8c;
  }

  .proof-wrapper .pdf-icon {
    font-size: 24px;
    color: #ff4d4f;
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .proof-wrapper .pdf-icon:hover {
    color: #ff7875;
  }

  .proof-thumbnails {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .proof-thumb {
    cursor: pointer;
    transition: transform 0.2s ease;
  }

  .proof-thumb:hover {
    transform: scale(1.1);
  }

  .pdf-thumb {
    font-size: 16px;
    color: #ff4d4f;
    padding: 4px;
    border-radius: 2px;
    background: #fff2f0;
  }

  .image-thumb {
    border-radius: 3px;
    object-fit: cover;
    border: 1px solid #e8e8e8;
  }

  .more-count {
    font-size: 11px;
    color: #8c8c8c;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
  }

  .more-count.small {
    font-size: 10px;
    padding: 1px 4px;
  }

  /* 证书文件区域 */
  .certificates-section {
    border-top: 1px solid #e8e8e8;
    padding-top: 12px;
  }

  .certificates-title {
    font-size: 12px;
    font-weight: 600;
    color: #595959;
    margin-bottom: 8px;
  }

  .certificates-grid {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .cert-group {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
  }

  .cert-label {
    font-size: 11px;
    color: #8c8c8c;
    min-width: 60px;
  }

  .cert-files {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .cert-thumb {
    cursor: pointer;
    transition: transform 0.2s ease;
  }

  .cert-thumb:hover {
    transform: scale(1.1);
  }

  .cert-image {
    border-radius: 2px;
    object-fit: cover;
    border: 1px solid #e8e8e8;
  }

  /* 无资产验证提示 */
  .no-assets {
    text-align: center;
    padding: 20px;
    color: #8c8c8c;
  }

  .no-assets-text {
    font-size: 13px;
  }
</style>
