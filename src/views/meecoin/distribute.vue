<template>
  <PageWrapper title="Recharge">
    <Card :bordered="false">
      <a-form :label-col="labelCol" :model="formState">
        <a-form-item label="Audience Type:">
          <a-radio-group v-model:value="formState.audienceType">
            <a-radio :style="radioStyle" value="all">All Users</a-radio>
            <a-radio :style="radioStyle" value="segment">Particular Users </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="formState.audienceType === 'segment'"
          label="Audiences: "
          :wrapper-col="{ span: 14 }"
        >
          <a-select
            v-model:value="formState.audiences"
            mode="multiple"
            label-in-value
            placeholder="search by username"
            :filter-option="false"
            :not-found-content="searchState.fetching ? undefined : null"
            :options="searchState.data"
            @search="fetchUser"
          >
            <template v-if="searchState.fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
          </a-select>
        </a-form-item>
        <a-form-item label="Amount:" :wrapper-col="{ span: 14 }">
          <a-input-number
            id="inputNumber"
            v-model:value="formState.amount"
            placeholder="Please enter the amount."
            :min="1"
            :max="9999999"
          />
        </a-form-item>
        <a-form-item label="Note:" :wrapper-col="{ span: 14 }">
          <a-textarea
            v-model:value="formState.note"
            placeholder="please enter the note. This note will be sent to the user."
            :auto-size="{ minRows: 4, maxRows: 6 }"
          />
        </a-form-item>
        <a-form-item
          :wrapper-col="{ span: 14, offset: 4 }"
          style="display: flex; justify-content: center; margin-top: 60px"
        >
          <a-popconfirm
            placement="topLeft"
            title="Are you sure you want to recharge?"
            ok-text="Yes"
            cancel-text="No"
            @confirm="onSubmit"
          >
            <a-button type="primary" :disabled="disabled"> Recharge </a-button>
          </a-popconfirm>
          <a-button style="margin-left: 10px" :disabled="disabled" @click="onReset">Reset</a-button>
        </a-form-item>
      </a-form>
    </Card>
  </PageWrapper>
</template>
<script lang="ts" name="distribute" setup>
  import { PageWrapper } from '/@/components/Page'
  import { Card, message, Popconfirm } from 'ant-design-vue'
  import { reactive, ref, watch } from 'vue'
  import { debounce } from 'lodash-es'
  import { getUsers } from '/@/api/users/users'
  import { distributeBalance } from '/@/api/users/users'
  import { SearchParams } from '/@/api/users/model/userModel'

  const APopconfirm = Popconfirm

  const labelCol = { style: { width: '200px' } }

  const formState = reactive<any>({
    audienceType: 'all',
    audiences: [],
    amount: '',
    note: '',
  })

  const disabled = ref(true)
  const isLoading = ref(false)

  const radioStyle = reactive({})

  const searchState = reactive<{ data: { label: string; value: number }[]; fetching: boolean }>({
    data: [],
    fetching: false,
  })

  const fetchUser = debounce(async (value) => {
    searchState.data = []
    searchState.fetching = true

    const params: SearchParams = {
      page: 1,
      pageSize: 10,
      username: value,
    }
    try {
      const res = await getUsers(params)
      const data = res.results.map((user) => ({
        label: user.username,
        value: user.id,
      }))
      searchState.data = data
      searchState.fetching = false
    } catch (error) {}
  }, 300)

  watch(formState.audiences, () => {
    searchState.data = []
    searchState.fetching = false
  })

  watch(formState, () => {
    disabled.value =
      !formState.amount ||
      (formState.audienceType === 'segment' && formState.audiences.length === 0)
  })

  const onSubmit = async () => {
    isLoading.value = true
    let audiences = []
    if (formState.audienceType === 'segment') {
      audiences = formState.audiences.map((el: { key: any; label: any }) => {
        return Number(el.key)
      })
    }
    try {
      await distributeBalance(formState.audienceType, audiences, formState.amount, formState.note)
      isLoading.value = false
      message.success(`Charge Successfully`)
      onReset()
    } catch (error) {
      isLoading.value = false
      message.error(`Charge Filed.`)
    }
  }

  const onReset = async () => {
    formState.audiences = []
    formState.audienceType = 'all'
    formState.amount = ''
    formState.note = ''
  }
</script>
