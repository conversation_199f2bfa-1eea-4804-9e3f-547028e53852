<template>
  <PageWrapper title="Users" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="username" label="Username">
                <a-input v-model:value="formState.username" placeholder="username" />
              </a-form-item>
            </a-col>

            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="type" label="Type">
                <a-select v-model:value="formState.type" :allowClear="true">
                  <a-select-option v-for="item in types" :value="item.value" :key="item.value">{{
                    item.label
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="mb-4 p-4 bg-white">
        <div style="margin-bottom: 16px">
          <a-space wrap>
            <a-button @click="fetchData"> refresh </a-button>
            <a-button type="primary" @click="onRecharge"> Recharge </a-button>
          </a-space>
        </div>
        <a-table
          :dataSource="dataSource"
          :pagination="pagination"
          @change="onChange"
          :loading="isLoading"
          size="small"
          :scroll="{ x: 1300 }"
        >
          <a-table-column key="customer" title="User" data-index="customer">
            <template #customRender="{ record }">
              <a-button
                type="link"
                @click="showDetail(record.customer.id, record.customer.username)"
                >{{ record.customer.username }}</a-button
              >
            </template>
          </a-table-column>

          <a-table-column key="type" title="Operation Type" data-index="type" />
          <a-table-column key="amount" title="Amount" data-index="amount" />
          <a-table-column key="balance" title="Balance" data-index="balance" />
          <a-table-column key="note" title="Note" data-index="ex" ellipsis="true">
            <template #customRender="{ record }"> {{ record?.ex?.note ?? '-' }} </template>
          </a-table-column>
          <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
            <template #customRender="{ record }">
              {{ formatToDateTime(record.createdAt) }}
            </template>
          </a-table-column>
        </a-table>
      </div>
    </div>
    <DistributeModal @register="distributeModalRegister" @finished="onModalClosed" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import type { FormInstance } from 'ant-design-vue'
  import { useModal } from '/@/components/Modal'
  import type { Dayjs } from 'dayjs'
  import { MeecoinSearchParams, TransactionType } from '/@/api/meecoin/model/meecoinModel'
  import { getTransactions } from '/@/api/meecoin/meecoin'
  import DistributeModal from '../users/components/DistributeModal.vue'

  const [distributeModalRegister, { openModal: openDistributeModal }] = useModal()

  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<any[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 20,
    position: ['bottomCenter'],
  })

  const types = ref<{ label: string; value: TransactionType }[]>(
    Object.keys(TransactionType).map((element) => {
      return { label: element, value: TransactionType[element] }
    }),
  )

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showDetail = (id, username): void => {
    go({
      name: 'user',
      params: { id, username },
    })
  }

  const formState = reactive({
    username: '',
    type: '' as TransactionType,
    startDate: '',
    endDate: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: MeecoinSearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      username: formState.username,
      type: formState.type,
      startDate: formState.startDate,
      endDate: formState.endDate,
    }

    try {
      const res = await getTransactions(params)
      console.log(res)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    fetchData()
  })

  const onRecharge = () => {
    openDistributeModal(true, {
      audienceType: 'all',
      selectedUsers: [],
    })
  }

  const onModalClosed = () => {
    fetchData()
  }
</script>
