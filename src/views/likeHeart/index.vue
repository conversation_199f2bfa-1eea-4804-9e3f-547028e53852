<template>
  <PageWrapper title="Sent hearts" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="sender" label="Sender">
                <a-input v-model:value="formState.sendername" placeholder="Sender" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="receivername" label="Receiver Name">
                <a-input v-model:value="formState.receivername" placeholder="Receiver Name" />
              </a-form-item>
            </a-col>

            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="level" label="Level">
                <a-select v-model:value="formState.level" :allowClear="true">
                  <a-select-option v-for="item in levels" :value="item.value" :key="item.value">{{
                    item.label
                  }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        size="small"
      >
        <a-table-column key="photos" title=" Avatar" data-index="sender" :width="80">
          <template #customRender="{ record }">
            <div
              class="table-image mx-auto transform text-center hover:scale-110 duration-300 ease-in-out overflow-hidden w-50px h-50px"
            >
              <a-image
                :width="50"
                :height="50"
                :src="record.sender?.avatar?.formats?.webp?.url || record.sender?.avatar?.url"
              />
            </div>
          </template>
        </a-table-column>
        <a-table-column key="sender" title="Sender" data-index="sender">
          <template #customRender="{ record }">
            <a-button type="link" @click="showDetail(record.sender?.id, record.sender?.username)">{{
              record.sender?.username
            }}</a-button>
          </template>
        </a-table-column>

        <a-table-column key="senderavatar" title=" Avatar" data-index="receiver" :width="80">
          <template #customRender="{ record }">
            <div
              class="table-image mx-auto transform text-center hover:scale-110 duration-300 ease-in-out overflow-hidden w-50px h-50px"
            >
              <a-image
                :width="50"
                :height="50"
                :src="record.receiver?.avatar?.formats?.webp?.url || record.receiver?.avatar?.url"
              />
            </div>
          </template>
        </a-table-column>
        <a-table-column key="receiver" title="Receiver" data-index="receiver">
          <template #customRender="{ record }">
            <a-button
              type="link"
              @click="showDetail(record.receiver?.id, record.receiver?.username)"
              >{{ record.receiver?.username }}</a-button
            >
          </template>
        </a-table-column>

        <a-table-column key="level" title="Level" data-index="level">
          <template #customRender="{ record }">
            <a-tag color="purple" v-if="record.level === LikeLevel.normal">Normal</a-tag>
            <a-tag color="orange" v-if="record.level === LikeLevel.double">Double</a-tag>
          </template>
        </a-table-column>

        <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
      </a-table>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import { getLikeHearts } from '/@/api/likeHeart/likeHeart'
  import { LikeHeartSearchParams, LikeLevel } from '/@/api/likeHeart/model/likeHeartModel'

  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<any[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const levels = ref<{ label: string; value: LikeLevel }[]>(
    Object.keys(LikeLevel).map((element) => {
      return { label: element, value: LikeLevel[element] }
    }),
  )

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showDetail = (id, username): void => {
    go({
      name: 'user',
      params: { id, username },
    })
  }

  const formState = reactive({
    sendername: '',
    receivername: '',
    level: '' as LikeLevel,
    startDate: '',
    endDate: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: LikeHeartSearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      sendername: formState.sendername,
      receivername: formState.receivername,
      level: formState.level,
      startDate: formState.startDate,
      endDate: formState.endDate,
    }

    try {
      const res = await getLikeHearts(params)
      console.log(res)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    fetchData()
  })
</script>
