<template>
  <PageWrapper title="Users" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="username" label="Username">
                <a-input v-model:value="formState.username" placeholder="username" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="friendname" label="FriendName">
                <a-input v-model:value="formState.friendname" placeholder="Friend Name" />
              </a-form-item>
            </a-col>
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        size="small"
      >
        <a-table-column key="useravatar" title=" Avatar" data-index="user" :width="80">
          <template #customRender="{ record }">
            <div
              class="table-image mx-auto transform text-center hover:scale-110 duration-300 ease-in-out overflow-hidden w-50px h-50px"
            >
              <a-image
                :width="50"
                :height="50"
                :src="record.user?.avatar?.formats?.webp?.url || record.user?.avatar?.url"
              />
            </div>
          </template>
        </a-table-column>
        <a-table-column key="Users" title="Username" data-index="user">
          <template #customRender="{ record }">
            <a-button type="link" @click="showDetail(record.user?.id, record.user?.username)">{{
              record.user?.username
            }}</a-button>
          </template>
        </a-table-column>
        <a-table-column key="friendavatar" title=" Avatar" data-index="friend" :width="80">
          <template #customRender="{ record }">
            <div
              class="table-image mx-auto transform text-center hover:scale-110 duration-300 ease-in-out overflow-hidden w-50px h-50px"
            >
              <a-image
                :width="50"
                :height="50"
                :src="record.friend?.avatar?.formats?.webp?.url || record.friend?.avatar?.url"
              />
            </div>
          </template>
        </a-table-column>
        <a-table-column key="friend" title="Friend Name" data-index="friend">
          <template #customRender="{ record }">
            <a-button type="link" @click="showDetail(record.friend?.id, record.friend?.username)">{{
              record.friend?.username
            }}</a-button>
          </template>
        </a-table-column>

        <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
      </a-table>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { useGo } from '/@/hooks/web/usePage'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import { MatchedModel, MatchedSearchParams } from '/@/api/matched/model/matchedModel'
  import { getMatchedUsers } from '/@/api/matched/matched'

  const go = useGo()
  const isLoading = ref(false)
  const dataSource = ref<MatchedModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const showDetail = (id, username): void => {
    go({
      name: 'user',
      params: { id, username },
    })
  }

  const formState = reactive({
    username: '',
    friendname: '',
    startDate: '',
    endDate: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: MatchedSearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      username: formState.username,
      friendname: formState.friendname,
      startDate: formState.startDate,
      endDate: formState.endDate,
    }

    try {
      const res = await getMatchedUsers(params)
      console.log(res)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  onMounted(() => {
    fetchData()
  })
</script>
