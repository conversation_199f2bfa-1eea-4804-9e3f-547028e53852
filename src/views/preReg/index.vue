<template>
  <PageWrapper title="Pre-reg" content="Pre register users">
    <div class="p-4">
      <!-- <a-table :dataSource="dataSource" :columns="columns" /> -->
      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
      >
        <a-table-column key="nickname" title="Nickname" data-index="nickname" />
        <a-table-column key="realname" title="Real Name" data-index="realname" />
        <a-table-column key="email" title="Email" data-index="email" />
        <a-table-column key="phone" title="Phone" data-index="phone">
          <template #customRender="{ record }">
            {{ record.countryCode }} {{ record.phone }}
          </template>
        </a-table-column>
        <a-table-column key="university" title="University" data-index="university">
          <template #customRender="{ record }">
            {{ record.university.name }}
          </template>
        </a-table-column>
        <a-table-column key="photos" title="Photos" data-index="photo">
          <template #customRender="{ record }">
            <div class="flex items-center">
              <div
                class="mr-1 transform hover:scale-110 duration-300 ease-in-out"
                v-for="image in record.photos"
                :key="image.id"
              >
                <a-image
                  class.native="transform hover:scale-105 duration-300 ease-linear"
                  :width="50"
                  :src="image.url"
                />
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column key="createdAt" title="Created Time" data-index="createdAt">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
      </a-table>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { getPreRegUsers } from '/@/api/preReg/users'
  import { formatToDateTime } from '/@/utils/dateUtil'

  const isLoading = ref(false)
  const dataSource = ref([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const fetchData = async () => {
    isLoading.value = true
    const params = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      sort: 'id:DESC',
    }
    try {
      const res = await getPreRegUsers(params)
      console.log(res)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      console.error(error)
      isLoading.value = false
    }
  }
  fetchData()

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }
</script>
