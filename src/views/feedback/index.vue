<template>
  <PageWrapper title="Users" content="">
    <div class="p-2">
      <div class="mb-4 p-4 bg-white">
        <a-form ref="searchFilterRef" name="Filters" :model="formState" @finish="onFinish">
          <a-row :gutter="24">
            <a-col :xxl="6" :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <a-form-item name="dateRange" label="Date Range">
                <a-range-picker @change="onRangeChange" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right">
              <a-button type="primary" html-type="submit">Search</a-button>
              <a-button style="margin: 0 8px" @click="resetFilter">Clear</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <a-table
        :dataSource="dataSource"
        :pagination="pagination"
        @change="onChange"
        :loading="isLoading"
        :scroll="{ x: 1400 }"
      >
        <a-table-column key="id" title="ID" data-index="id" :width="80" />
        <a-table-column key="email" title="Email" data-index="email" :width="160" ellipsis="true">
          <template #customRender="{ record }">
            <a :href="`mailto:${record?.email}`"
              ><mail-outlined class="text-xl" />{{ record.email }}</a
            >
          </template>
        </a-table-column>
        <a-table-column
          key="category"
          title="Category"
          data-index="category"
          :width="300"
          ellipsis="true"
        >
          <template #customRender="{ record }">
            {{ record.category?.name }}
          </template>
        </a-table-column>
        <a-table-column key="content" title="Content" data-index="content" ellipsis="true">
          <template #customRender="{ record }">
            <a-tooltip placement="topLeft" :title="record.content" color="#1D4ED8">
              {{ record.content || '-' }}
            </a-tooltip>
          </template>
        </a-table-column>

        <a-table-column key="attachments" title="Attachments" data-index="attachments" :width="200">
          <template #customRender="{ record }">
            <a-image-preview-group>
              <a-image
                :width="40"
                :height="40"
                v-for="image in record?.attachments"
                :key="image.id"
                :src="image.formats?.webp?.url || image.url"
              />
            </a-image-preview-group>
          </template>
        </a-table-column>

        <a-table-column key="createdAt" title="Created Time" data-index="createdAt" :width="160">
          <template #customRender="{ record }">
            {{ formatToDateTime(record.createdAt) }}
          </template>
        </a-table-column>
        <a-table-column key="operation" title="Action" fixed="right" :width="100">
          <template #customRender="{ record }">
            <a-button type="link" size="small" @click="reply(record.email)">Reply</a-button>
          </template>
        </a-table-column>
      </a-table>
    </div>
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { PageWrapper } from '/@/components/Page'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import type { FormInstance } from 'ant-design-vue'
  import type { Dayjs } from 'dayjs'
  import { FeedbackModel, FeedbackSearchParams } from '/@/api/feedback/model/feedbackModel'
  import { getFeedbacks } from '/@/api/feedback/feedback'

  const isLoading = ref(false)
  const dataSource = ref<FeedbackModel[]>([])
  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    position: ['bottomCenter'],
  })

  const onChange = (pageChanged) => {
    console.log(pagination)
    pagination.value = { ...pageChanged }
    fetchData()
  }

  const formState = reactive({
    startDate: '',
    endDate: '',
  })
  const onRangeChange = (value: [Dayjs, Dayjs], dateString: [string, string]) => {
    console.log('Selected Time: ', value)
    console.log('Formatted Selected Time: ', dateString)
    formState.startDate = dateString[0]
    formState.endDate = dateString[1]
  }

  const searchFilterRef = ref<FormInstance>()
  const resetFilter = () => searchFilterRef.value!.resetFields()
  const onFinish = () => {
    pagination.value.current = 1
    fetchData()
  }

  const fetchData = async () => {
    isLoading.value = true
    const params: FeedbackSearchParams = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      startDate: formState.startDate,
      endDate: formState.endDate,
    }

    try {
      const res = await getFeedbacks(params)
      dataSource.value = res.results
      pagination.value.total = res.pagination.total
      isLoading.value = false
    } catch (error) {
      isLoading.value = false
    }
  }

  const reply = async (email) => {
    const mailToUrl = `mailto:${encodeURIComponent(email)}`
    window.open(mailToUrl, '_blank')
  }
  onMounted(() => {
    fetchData()
  })
</script>
<style scoped>
  :deep(.ant-image-img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
