<template>
  <BasicModal
    v-bind="$attrs"
    width="600px"
    :wrapperFooterOffset="1"
    useWrapper
    @register="register"
    :title="t('admin.profile.title')"
    :ok-button-props="{ disabled: disabled }"
    :ok-text="t('admin.profile.save')"
    @cancel="onReset"
    @ok="onSubmit"
  >
    <div class="pt-3px pr-3px">
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="labelCol"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item :label="t('admin.profile.name')" name="name">
              <a-input v-model:value="formState.name" :placeholder="t('admin.profile.namePlaceholder')" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item :label="t('admin.profile.position')" name="position">
              <a-input v-model:value="formState.position" :placeholder="t('admin.profile.positionPlaceholder')" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item :label="t('admin.profile.avatar')" name="avatar" required>
              <div class="clearfix">
                <a-upload
                  v-model:file-list="avatarFileList"
                  accept="image/*"
                  name="files"
                  :headers="headers"
                  list-type="picture-card"
                  :action="uploadUrl"
                  @change="handleAvatarChange"
                  @preview="handleAvatarPreview"
                >
                  <div v-if="avatarFileList?.length < 1">
                    <plus-outlined />
                    <div style="margin-top: 8px">{{ t('admin.profile.uploadAvatar') }}</div>
                  </div>
                </a-upload>
                <a-modal
                  :visible="avatarPreviewVisible"
                  :footer="null"
                  @cancel="handleAvatarPreviewCancel"
                >
                  <img :alt="t('admin.profile.avatarPreview')" style="width: 100%" :src="avatarPreviewUrl" />
                </a-modal>
              </div>
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item :label="t('admin.profile.signature')" name="signature" required>
              <div class="clearfix">
                <a-upload
                  v-model:file-list="signatureFileList"
                  accept="image/*"
                  name="files"
                  :headers="headers"
                  list-type="picture-card"
                  :action="uploadUrl"
                  @change="handleSignatureChange"
                  @preview="handleSignaturePreview"
                >
                  <div v-if="signatureFileList?.length < 1">
                    <plus-outlined />
                    <div style="margin-top: 8px">{{ t('admin.profile.uploadSignature') }}</div>
                  </div>
                </a-upload>
                <a-modal
                  :visible="signaturePreviewVisible"
                  :footer="null"
                  @cancel="handleSignaturePreviewCancel"
                >
                  <img :alt="t('admin.profile.signaturePreview')" style="width: 100%" :src="signaturePreviewUrl" />
                </a-modal>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { computed, reactive, ref } from 'vue'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { message, type FormInstance, type UploadChangeParam } from 'ant-design-vue'
  import { useGlobSetting } from '/@/hooks/setting'
  import { getToken } from '/@/utils/auth'
  import { useUserStore } from '/@/store/modules/user'
  import { useI18n } from '/@/hooks/web/useI18n'
  import type { AdminProfileModel } from '/@/api/sys/model/userModel'

  const emit = defineEmits(['finished', 'register'])

  const { t } = useI18n()
  const userStore = useUserStore()
  const formRef = ref<FormInstance>()
  const labelCol = { style: { width: '80px' } }

  // 表单状态
  const formState = reactive({
    name: '',
    position: '',
    avatarId: null as number | null,
    signatureId: null as number | null,
  })

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: t('admin.profile.nameRequired'), trigger: 'blur' }],
    position: [{ required: true, message: t('admin.profile.positionRequired'), trigger: 'blur' }],
    avatar: [{ required: true, message: t('admin.profile.avatarRequired'), trigger: 'change' }],
    signature: [{ required: true, message: t('admin.profile.signatureRequired'), trigger: 'change' }],
  }

  // 上传配置
  const headers = {
    Authorization: `Bearer ${getToken()}`,
  }
  const uploadUrl = useGlobSetting().apiUrl + '/upload'

  // 头像上传相关
  const avatarFileList = ref<any[]>([])
  const avatarPreviewVisible = ref(false)
  const avatarPreviewUrl = ref('')

  // 签名上传相关
  const signatureFileList = ref<any[]>([])
  const signaturePreviewVisible = ref(false)
  const signaturePreviewUrl = ref('')

  // 表单验证状态
  const disabled = computed(() => {
    return !formState.name || !formState.position
  })

  // 弹窗注册
  const [register, { closeModal, changeOkLoading }] = useModalInner((data: AdminProfileModel) => {
    if (data) {
      onDataReceive(data)
    }
  })

  // 接收数据并初始化表单
  const onDataReceive = (data: AdminProfileModel) => {
    formState.name = data.name || ''
    formState.position = data.position || ''
    
    // 初始化头像
    if (data.avatar?.url) {
      avatarFileList.value = [{
        uid: '-1',
        name: 'avatar.png',
        status: 'done',
        url: data.avatar.url,
      }]
    }
    
    // 初始化签名
    if (data.signature?.url) {
      signatureFileList.value = [{
        uid: '-2',
        name: 'signature.png',
        status: 'done',
        url: data.signature.url,
      }]
    }
  }

  // 头像上传处理
  const handleAvatarChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const id = info.file.response[0].id
      formState.avatarId = id
      message.success(t('admin.profile.avatarUploadSuccess'))
    } else if (info.file.status === 'error') {
      message.error(t('admin.profile.avatarUploadFailed'))
    }
  }

  const handleAvatarPreview = async (file: any) => {
    avatarPreviewUrl.value = file.url || file.preview
    avatarPreviewVisible.value = true
  }

  const handleAvatarPreviewCancel = () => {
    avatarPreviewVisible.value = false
  }

  // 签名上传处理
  const handleSignatureChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const id = info.file.response[0].id
      formState.signatureId = id
      message.success(t('admin.profile.signatureUploadSuccess'))
    } else if (info.file.status === 'error') {
      message.error(t('admin.profile.signatureUploadFailed'))
    }
  }

  const handleSignaturePreview = async (file: any) => {
    signaturePreviewUrl.value = file.url || file.preview
    signaturePreviewVisible.value = true
  }

  const handleSignaturePreviewCancel = () => {
    signaturePreviewVisible.value = false
  }

  // 重置表单
  const onReset = () => {
    formRef.value?.resetFields()
    avatarFileList.value = []
    signatureFileList.value = []
    formState.avatarId = null
    formState.signatureId = null
  }

  // 提交表单
  const onSubmit = async () => {
    try {
      await formRef.value?.validate()
      
      if (!formState.avatarId || !formState.signatureId) {
        message.error(t('admin.profile.uploadRequired'))
        return
      }

      changeOkLoading(true)

      await userStore.updateAdminProfileAction({
        name: formState.name,
        position: formState.position,
        avatar: { id: formState.avatarId },
        signature: { id: formState.signatureId },
      })

      message.success(t('admin.profile.updateSuccess'))
      closeModal()
      emit('finished')
    } catch (error) {
      console.error('更新失败:', error)
      message.error(t('admin.profile.updateFailed'))
    } finally {
      changeOkLoading(false)
    }
  }
</script>

<style scoped>
  .clearfix::after {
    content: '';
    display: table;
    clear: both;
  }
</style>
