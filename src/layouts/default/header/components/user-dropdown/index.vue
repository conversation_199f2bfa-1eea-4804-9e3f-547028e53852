<template>
  <Dropdown placement="bottomLeft" :overlayClassName="`${prefixCls}-dropdown-overlay`">
    <span :class="[prefixCls, `${prefixCls}--${theme}`]" class="flex">
      <img :class="`${prefixCls}__header`" :src="getUserInfo.avatar" />
      <span :class="`${prefixCls}__info hidden md:block`">
        <span :class="`${prefixCls}__name  `" class="truncate">
          {{ getUserInfo.realName }}
        </span>
      </span>
    </span>

    <template #overlay>
      <Menu @click="handleMenuClick">
        <MenuItem key="edit" :text="t('layout.header.dropdownItemEditProfile')" icon="ion:person-outline" />
        <!-- <MenuItem
          key="doc"
          :text="t('layout.header.dropdownItemDoc')"
          icon="ion:document-text-outline"
          v-if="getShowDoc"
        /> -->
        <MenuDivider v-if="getShowDoc" />
        <MenuItem
          key="logout"
          :text="t('layout.header.dropdownItemLoginOut')"
          icon="ion:power-outline"
        />
      </Menu>
    </template>
  </Dropdown>

  <!-- 管理员个人信息编辑弹窗 -->
  <AdminProfileModal @register="registerModal" @finished="handleModalFinished" />
</template>
<script lang="ts">
  // components
  import { Dropdown, Menu } from 'ant-design-vue'
  import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface'

  import { defineComponent, computed, onMounted } from 'vue'

  import { DOC_URL } from '/@/settings/siteSetting'

  import { useUserStore } from '/@/store/modules/user'
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting'
  import { useI18n } from '/@/hooks/web/useI18n'
  import { useDesign } from '/@/hooks/web/useDesign'
  import { useModal } from '/@/components/Modal'

  import headerImg from '/@/assets/images/header.jpg'
  import { propTypes } from '/@/utils/propTypes'
  import { openWindow } from '/@/utils'

  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent'
  import AdminProfileModal from './AdminProfileModal.vue'

  type MenuEvent = 'logout' | 'doc' | 'edit'

  export default defineComponent({
    name: 'UserDropdown',
    components: {
      Dropdown,
      Menu,
      MenuItem: createAsyncComponent(() => import('./DropMenuItem.vue')),
      MenuDivider: Menu.Divider,
      AdminProfileModal,
    },
    props: {
      theme: propTypes.oneOf(['dark', 'light']),
    },
    setup() {
      const { prefixCls } = useDesign('header-user-dropdown')
      const { t } = useI18n()
      const { getShowDoc } = useHeaderSetting()
      const userStore = useUserStore()

      const getUserInfo = computed(() => {
        const adminProfile = userStore.getAdminProfile
        if (adminProfile) {
          // 使用管理员个人信息
          return {
            realName: adminProfile.name,
            avatar: adminProfile.avatar?.url || headerImg,
            desc: adminProfile.position,
          }
        }
        // 回退到原有的用户信息
        const { realName = '', avatar, desc } = userStore.getUserInfo || {}
        return { realName, avatar: avatar || headerImg, desc }
      })

      const [register] = useModal()
      const [registerModal, { openModal }] = useModal()

      // 获取管理员个人信息
      onMounted(() => {
        userStore.getAdminProfileAction()
      })

      // 打开编辑个人信息弹窗
      function handleEditProfile() {
        const adminProfile = userStore.getAdminProfile
        if (adminProfile) {
          openModal(true, adminProfile)
        } else {
          // 如果没有管理员信息，先获取
          userStore.getAdminProfileAction().then((profile) => {
            if (profile) {
              openModal(true, profile)
            }
          })
        }
      }

      // 弹窗完成回调
      function handleModalFinished() {
        // 可以在这里添加刷新逻辑或其他处理
        console.log('管理员信息更新完成')
      }

      //  login out
      function handleLoginOut() {
        userStore.confirmLoginOut()
      }

      // open doc
      function openDoc() {
        openWindow(DOC_URL)
      }

      function handleMenuClick(e: MenuInfo) {
        switch (e.key as MenuEvent) {
          case 'logout':
            handleLoginOut()
            break
          case 'doc':
            openDoc()
            break
          case 'edit':
            handleEditProfile()
            break
        }
      }

      return {
        prefixCls,
        t,
        getUserInfo,
        handleMenuClick,
        getShowDoc,
        register,
        registerModal,
        handleEditProfile,
        handleModalFinished,
      }
    },
  })
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-user-dropdown';

  .@{prefix-cls} {
    height: @header-height;
    padding: 0 0 0 10px;
    padding-right: 10px;
    overflow: hidden;
    font-size: 12px;
    cursor: pointer;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }

    &__header {
      border-radius: 50%;
    }

    &__name {
      font-size: 14px;
    }

    &--dark {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    &--light {
      &:hover {
        background-color: @header-light-bg-hover-color;
      }

      .@{prefix-cls}__name {
        color: @text-color-base;
      }

      .@{prefix-cls}__desc {
        color: @header-light-desc-color;
      }
    }

    &-dropdown-overlay {
      .ant-dropdown-menu-item {
        min-width: 160px;
      }
    }
  }
</style>
