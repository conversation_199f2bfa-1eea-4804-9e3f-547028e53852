import { BasicPageParams, SourceFile } from '../../model/baseModel'

// Benefits Board Models
export interface BenefitsBoardModel {
  id: number
  name: string
  locales?: {
    en?: string
    ko?: string
    [key: string]: string | undefined
  }
  description?: string
  related?: string
  blocked: boolean
  type: 'public' | 'private'
  creator?: {
    id: number
    username: string
    email: string
  }
  postingPermission?: number
  subscribers?: Array<{
    id: number
    username: string
    email: string
  }>
  coupons?: BenefitsCouponModel[]
  order?: number
  isDefault: boolean
  userLevelLimited?: string
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
}

export interface BenefitsBoardCreateParams {
  name: string
  locales?: {
    en?: string
    ko?: string
    [key: string]: string | undefined
  }
  description?: string
  related?: string
  blocked?: boolean
  type: 'public' | 'private'
  postingPermission?: number
  order?: number
  isDefault?: boolean
  userLevelLimited?: string
}

export interface BenefitsBoardUpdateParams extends BenefitsBoardCreateParams {
  id: number
}

export interface BenefitsBoardSearchParams extends BasicPageParams {
  name?: string
  type?: 'public' | 'private'
  blocked?: boolean
  isDefault?: boolean
  startDate?: string
  endDate?: string
}

// Benefits Coupon Models
export interface BenefitsCouponModel {
  id: number
  title: string
  content?: string
  media?: SourceFile[]
  pinned: boolean
  blocked: boolean
  removed: boolean
  likeCount: number
  board?: {
    id: number
    name: string
    type: string
  }
  likers?: Array<{
    id: number
    username: string
    email: string
  }>
  expiresAt?: string
  phone?: string
  level?: number
  description?: string
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
}

export interface BenefitsCouponCreateParams {
  title: string
  content?: string
  media?: number[]
  pinned?: boolean
  blocked?: boolean
  removed?: boolean
  board?: number
  expiresAt?: string
  phone?: string
  level?: number
  description?: string
}

export interface BenefitsCouponUpdateParams extends BenefitsCouponCreateParams {
  id: number
}

export interface BenefitsCouponSearchParams extends BasicPageParams {
  title?: string
  boardId?: number
  pinned?: boolean
  blocked?: boolean
  removed?: boolean
  level?: number
  startDate?: string
  endDate?: string
  expiresAfter?: string
  expiresBefore?: string
}
