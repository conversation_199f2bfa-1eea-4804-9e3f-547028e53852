import { defHttp } from '/@/utils/http/axios'
import { BasicListFetchResult, OperationResponse } from '../model/baseModel'
import {
  BenefitsBoardModel,
  BenefitsBoardCreateParams,
  BenefitsBoardUpdateParams,
  BenefitsBoardSearchParams,
  BenefitsCouponModel,
  BenefitsCouponCreateParams,
  BenefitsCouponUpdateParams,
  BenefitsCouponSearchParams,
} from './model/benefitsModels'

enum Api {
  // Benefits Boards
  boards = '/manager/benefits/boards',
  board = '/manager/benefits/boards/{id}',

  // Benefits Coupons
  coupons = '/manager/benefits/coupons',
  coupon = '/manager/benefits/coupons/{id}',
}

// Benefits Boards API
export function getBenefitsBoards(params: BenefitsBoardSearchParams) {
  return defHttp.get<BasicListFetchResult<BenefitsBoardModel>>(
    { url: Api.boards, params },
    { errorMessageMode: 'none' },
  )
}

export function getBenefitsBoard(id: number) {
  return defHttp.get<BenefitsBoardModel>(
    { url: Api.board.replace('{id}', id.toString()) },
    { errorMessageMode: 'none' },
  )
}

export function createBenefitsBoard(data: BenefitsBoardCreateParams) {
  return defHttp.post<OperationResponse>(
    {
      url: Api.boards,
      data,
    },
    { errorMessageMode: 'none' },
  )
}

export function updateBenefitsBoard(id: number, data: BenefitsBoardUpdateParams) {
  return defHttp.put<OperationResponse>(
    {
      url: Api.board.replace('{id}', id.toString()),
      data,
    },
    { errorMessageMode: 'none' },
  )
}

export function deleteBenefitsBoard(id: number) {
  return defHttp.delete<OperationResponse>(
    { url: Api.board.replace('{id}', id.toString()) },
    { errorMessageMode: 'none' },
  )
}

export function blockBenefitsBoard(id: number) {
  return defHttp.put<OperationResponse>(
    {
      url: Api.board.replace('{id}', id.toString()),
      data: { blocked: true },
    },
    { errorMessageMode: 'none' },
  )
}

// Benefits Coupons API
export function getBenefitsCoupons(params: BenefitsCouponSearchParams) {
  return defHttp.get<BasicListFetchResult<BenefitsCouponModel>>(
    { url: Api.coupons, params },
    { errorMessageMode: 'none' },
  )
}

export function getBenefitsCoupon(id: number) {
  return defHttp.get<BenefitsCouponModel>(
    { url: Api.coupon.replace('{id}', id.toString()) },
    { errorMessageMode: 'none' },
  )
}

export function createBenefitsCoupon(data: BenefitsCouponCreateParams) {
  return defHttp.post<OperationResponse>(
    {
      url: Api.coupons,
      data,
    },
    { errorMessageMode: 'none' },
  )
}

export function updateBenefitsCoupon(id: number, data: BenefitsCouponUpdateParams) {
  return defHttp.put<OperationResponse>(
    {
      url: Api.coupon.replace('{id}', id.toString()),
      data,
    },
    { errorMessageMode: 'none' },
  )
}

export function deleteBenefitsCoupon(id: number) {
  return defHttp.delete<OperationResponse>(
    { url: Api.coupon.replace('{id}', id.toString()) },
    { errorMessageMode: 'none' },
  )
}
