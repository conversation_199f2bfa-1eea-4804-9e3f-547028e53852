/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  email: string
  password: string
}

export interface RoleInfo {
  roleName: string
  value: string
}

/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  data: {
    token: string
    user: UserModel
  }
}

/**
 * @description: Get user information return value
 */
export interface GetUserInfoModel {
  roles: RoleInfo[]
  // 用户id
  userId: string | number
  // 用户名
  username: string
  // 真实名字
  realName: string
  // 头像
  avatar: string
  // 介绍
  desc?: string
}

export interface UserModel {
  id: number
  firstname: string
  lastname: string
  username: string
  email: string
  isActivate: boolean
  blocked: boolean
  createdAt: string
  updatedAt: string
}

/**
 * @description: 管理员个人信息数据模型
 */
export interface AdminProfileModel {
  name: string
  position: string
  signature: {
    url: string
  }
  avatar: {
    url: string
  }
}

/**
 * @description: 更新管理员个人信息请求参数
 */
export interface UpdateAdminProfileParams {
  name: string
  position: string
  signature: {
    id: number
  }
  avatar: {
    id: number
  }
}
