import { defHttp } from '/@/utils/http/axios'
import { LoginParams, LoginResultModel, GetUserInfoModel, AdminProfileModel, UpdateAdminProfileParams } from './model/userModel'

import { ErrorMessageMode } from '/#/axios'

enum Api {
  Login = '/admin/login',
  Logout = '/admin/logout',
  GetUserInfo = '/getUserInfo',
  GetAdminProfile = '/manager/users/me/profile',
  GetPermCode = '/getPermCode',
  TestRetry = '/testRetry',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  )
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' })
}

/**
 * @description: 获取管理员个人信息
 */
export function getAdminProfile() {
  return defHttp.get<AdminProfileModel>({ url: Api.GetAdminProfile }, { errorMessageMode: 'none' })
}

/**
 * @description: 更新管理员个人信息
 */
export function updateAdminProfile(params: UpdateAdminProfileParams) {
  return defHttp.post<AdminProfileModel>({ url: Api.GetAdminProfile, data: params }, { errorMessageMode: 'none' })
}

export function getPermCode() {
  return defHttp.get<string[]>({ url: Api.GetPermCode })
}

export function doLogout() {
  return defHttp.post({ url: Api.Logout })
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    },
  )
}
