import { defHttp } from '/@/utils/http/axios'
import { BasicListFetchResult, BasicPageParams } from '../model/baseModel'
import { MessageModel } from './model/messageModel'

enum Api {
  list = '/messages',
  create = '/messages',
}

export function getMessages(params: BasicPageParams) {
  return defHttp.get<BasicListFetchResult<MessageModel>>(
    { url: Api.list, params },
    { errorMessageMode: 'none' },
  )
}

export function postMessage(message: MessageModel) {
  return defHttp.post<any>({
    url: Api.create,
    data: message,
  })
}
