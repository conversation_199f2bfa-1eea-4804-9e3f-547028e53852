import { defHttp } from '/@/utils/http/axios'
import { BasicListFetchResult } from '../model/baseModel'
import { RecommendationSearchParams, RecommendationModel } from './model/dailyModel'

enum Api {
  rule = '/manager/recommendations/rules',
  recommendations = '/manager/recommendations',
}

export function getDailyRule() {
  return defHttp.get<[]>({ url: Api.rule }, { errorMessageMode: 'none' })
}

export function updateDailyRule(values: number[]) {
  return defHttp.post<[]>({
    url: Api.rule,
    data: { values },
  })
}

export function getRecommendations(params: RecommendationSearchParams) {
  return defHttp.get<BasicListFetchResult<RecommendationModel>>(
    { url: Api.recommendations, params },
    { errorMessageMode: 'none' },
  )
}
