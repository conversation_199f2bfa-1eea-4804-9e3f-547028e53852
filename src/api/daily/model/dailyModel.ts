import { ImageModel } from '../../model/baseModel'

export interface RecommendationSearchParams {
  page: number
  pageSize: number
  username?: string
  cardname?: string
  startDate?: string
  endDate?: string
  type?: string
}

export interface RecommendationModel {
  id: number
  type: string
  sequence: string
  recommender: User
  recommended: User
  createdAt: Date
}

export interface User {
  id: number
  username: string
  level: number
  avatar: ImageModel
}
