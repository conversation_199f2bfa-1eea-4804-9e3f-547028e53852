import { BasicListFetchResult } from '../model/baseModel'
import { LikeHeartModel, LikeHeartSearchParams } from './model/likeHeartModel'
import { defHttp } from '/@/utils/http/axios'
enum Api {
  likeHearts = '/manager/requests',
}

export function getLikeHearts(params: LikeHeartSearchParams) {
  return defHttp.get<BasicListFetchResult<LikeHeartModel>>(
    { url: Api.likeHearts, params },
    { errorMessageMode: 'none' },
  )
}
