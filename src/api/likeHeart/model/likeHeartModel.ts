export interface LikeHeartSearchParams {
  page: number
  pageSize: number
  sendername?: string
  receivername?: string
  level?: LikeLevel
  startDate?: string
  endDate?: string
}

export interface LikeHeartModel {
  id: number
  level: LikeLevel
  createdAt: string
  sender: User
  receiver: User
}

export interface User {
  id: number
  username: string
}

export enum LikeLevel {
  normal = 'low',
  double = 'high',
}
