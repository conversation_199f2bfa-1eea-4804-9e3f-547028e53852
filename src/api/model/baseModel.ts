export interface BasicPageParams {
  page: number
  pageSize: number
  sort?: string
}

export interface BasicFetchResult<T> {
  items: T[]
  total: number
}

export interface BasicListFetchResult<T> {
  results: T[]
  pagination: Pagination
}

export interface Pagination {
  page: number
  pageSize: number
  pageCount: number
  total: number
}

export interface OperationResponse {
  ok: boolean
}

export interface FinancialStatusUpdateResponse {
  ok: boolean
  message: string
  user: {
    id: number
    financialStatus: string
    level: number
  }
}

export enum Gender {
  F = 'F',
  M = 'M',
}

export interface SourceFile {
  id: number
  name: string
  url: string
  ext: string
  mime: string
  size: number
  formats?: {
    large?: ImageFormatModel
    small?: ImageFormatModel
    medium?: ImageFormatModel
    thumbnail?: ImageFormatModel
    webp?: ImageFormatModel
  }
}

export interface ImageModel {
  id: number
  url: string
}

export interface ImageFormatModel {
  ext: string
  url: string
  hash: string
  mime: string
  name: string
  path?: string | null
  size: number
  width: number
  height: number
}

export interface FormatsModel {
  webp: ImageModel
}
