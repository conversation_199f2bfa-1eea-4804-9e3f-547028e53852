export interface BasicPageParams {
  page: number
  pageSize: number
  sort?: string
}

export interface BasicFetchResult<T> {
  items: T[]
  total: number
}

export interface BasicListFetchResult<T> {
  results: T[]
  pagination: Pagination
}

export interface Pagination {
  page: number
  pageSize: number
  pageCount: number
  total: number
}

export interface OperationResponse {
  ok: boolean
}

export interface FinancialStatusUpdateResponse {
  ok: boolean
  message: string
  user: {
    id: number
    financialStatus: string
    level: number
  }
}

export enum Gender {
  F = 'F',
  M = 'M',
}

export interface SourceFile {
  id: number
  url: string
  formats: FormatsModel
}

export interface ImageModel {
  id: number
  url: string
}

export interface FormatsModel {
  webp: ImageModel
}
