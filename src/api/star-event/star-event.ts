import { defHttp } from '/@/utils/http/axios'
import { BasicListFetchResult, OperationResponse } from '../model/baseModel'
import { StarEventListParams, StarEventModel, StarEventMemberModel } from './model/starEventModels'

enum Api {
  events = '/manager/star-events',
  event = '/manager/star-events/{id}',
  eventMembers = '/manager/star-events/{id}/members',
  eventMember = '/manager/star-event-members/{id}',
  eventSections = '/manager/star-events/sections',
  eventList = '/manager/star-events/list',
  myPastEvents = '/manager/star-events/my-past',
  joinEvent = '/manager/star-events/{id}/join',
  approveJoin = '/manager/star-events/{id}/approve',
  rejectJoin = '/manager/star-events/{id}/reject',
}

// 获取活动列表
export function getStarEvents(params: StarEventListParams) {
  return defHttp.get<BasicListFetchResult<StarEventModel>>({ url: Api.events, params })
}

// 获取活动详情
export function getStarEvent(id: number) {
  return defHttp.get<StarEventModel>({ url: Api.event.replace('{id}', id.toString()) })
}

// 创建活动
export function createStarEvent(data: Partial<StarEventModel>) {
  return defHttp.post<OperationResponse>({ url: Api.events, data })
}

// 更新活动
export function updateStarEvent(id: number, data: Partial<StarEventModel>) {
  return defHttp.put<OperationResponse>({ url: Api.event.replace('{id}', id.toString()), data })
}

// 删除活动
export function deleteStarEvent(id: number) {
  return defHttp.delete<OperationResponse>({ url: Api.event.replace('{id}', id.toString()) })
}

// 获取活动成员列表
export function getStarEventMembers(
  id: number,
  params?: {
    status?: 'joined' | 'applied' | 'rejected'
    page?: number
    pageSize?: number
  },
) {
  return defHttp.get<BasicListFetchResult<StarEventMemberModel>>({
    url: Api.eventMembers.replace('{id}', id.toString()),
    params,
  })
}

// 更新活动成员状态
export function updateStarEventMemberStatus(id: number, status: string) {
  return defHttp.put<OperationResponse>({
    url: Api.eventMember.replace('{id}', id.toString()),
    data: { status },
  })
}

// 获取活动分类列表
export function getEventSections() {
  return defHttp.get({ url: Api.eventSections })
}

// 获取活动列表(支持筛选)
export function getEventList(params?: any) {
  return defHttp.get<BasicListFetchResult<StarEventModel>>({ url: Api.eventList, params })
}

// 获取我参加过的历史活动
export function getMyPastEvents(params?: any) {
  return defHttp.get<BasicListFetchResult<StarEventModel>>({ url: Api.myPastEvents, params })
}

// 加入活动
export function joinEvent(id: number, data?: any) {
  return defHttp.post<OperationResponse>({
    url: Api.joinEvent.replace('{id}', id.toString()),
    data,
  })
}

// 批准加入请求
export function approveJoinRequest(id: number, data?: any) {
  return defHttp.post<OperationResponse>({
    url: Api.approveJoin.replace('{id}', id.toString()),
    data,
  })
}

// 拒绝加入请求
export function rejectJoinRequest(id: number, data?: any) {
  return defHttp.post<OperationResponse>({
    url: Api.rejectJoin.replace('{id}', id.toString()),
    data,
  })
}
