import { SourceFile } from '../../model/baseModel'

export interface StarEventListParams {
  title?: string
  starName?: string
  type?: string
  startDateFrom?: string
  startDateTo?: string
  page?: number
  pageSize?: number
}

export interface StarEventMemberListParams {
  eventId: number
  status?: string
  username?: string
  page?: number
  pageSize?: number
}

export interface StarModel {
  id: number
  username: string
  fullname: string
  email: string
  level: number
  title?: string
  introduction?: string
  avatar?: SourceFile
}

export interface StarEventModel {
  id: number
  title: string
  description?: string
  address: string
  longitude?: number
  latitude?: number
  startDate: string
  maxMembers: number
  type: 'normal' | 'specialGuest'
  photos?: SourceFile[]
  star?: StarModel
  membersCount?: number
  createdAt: string
  updatedAt: string
  content: string
}

export interface StarEventMemberModel {
  id: number
  status: 'applied' | 'joined' | 'rejected'
  user: {
    id: number
    username: string
    email: string
    fullname?: string
    level?: number
    avatar?: SourceFile
  }
  event: {
    id: number
    title: string
  }
  createdAt: string
  updatedAt: string
}
