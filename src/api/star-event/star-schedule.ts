import { defHttp } from '/@/utils/http/axios'
import { BasicListFetchResult, OperationResponse, SourceFile } from '../model/baseModel'
import { StarEventModel } from './model/starEventModels'

// 为了避免创建新文件夹，直接在此定义类型
export interface StarScheduleListParams {
  playingDate?: string
  playingDateFrom?: string
  playingDateTo?: string
  eventId?: number
  page?: number
  pageSize?: number
}

export interface StarScheduleModel {
  id: number
  playingDate: string
  event?: StarEventModel
  previewImage?: SourceFile
  createdAt: string
  updatedAt: string
}

enum Api {
  schedules = '/manager/star-schedules',
  schedule = '/manager/star-schedules/{id}',
}

// 获取日程列表
export function getStarSchedules(params: StarScheduleListParams) {
  return defHttp.get<BasicListFetchResult<StarScheduleModel>>({ url: Api.schedules, params })
}

// 获取日程详情
export function getStarSchedule(id: number) {
  return defHttp.get<StarScheduleModel>({ url: Api.schedule.replace('{id}', id.toString()) })
}

// 创建日程
export function createStarSchedule(data: Partial<StarScheduleModel>) {
  return defHttp.post<OperationResponse>({ url: Api.schedules, data })
}

// 更新日程
export function updateStarSchedule(id: number, data: Partial<StarScheduleModel>) {
  return defHttp.put<OperationResponse>({ url: Api.schedule.replace('{id}', id.toString()), data })
}

// 删除日程
export function deleteStarSchedule(id: number) {
  return defHttp.delete<OperationResponse>({ url: Api.schedule.replace('{id}', id.toString()) })
}
