import { defHttp } from '/@/utils/http/axios'
import { BasicListFetchResult, BasicPageParams, OperationResponse, FinancialStatusUpdateResponse } from '../model/baseModel'
import {
  MeeCoinOperation,
  MeeCoinOperationResponse,
  University,
  UserModel,
  UserStatus,
  VerificationStatus,
  RejectionReason,
} from './model/userModel'

enum Api {
  users = '/manager/users',
  download = '/manager/users/search/download',
  universities = '/manager/universities',
  user = '/manager/users/{id}',
  setStatus = '/manager/users/{id}/verification',
  addMeeCoin = '/manager/mc/deposit',
  deductMeeCoin = '/manager/mc/deduct',
  distributeMeeCoin = '/manager/mc/distribute',
  changeLevel = '/manager/users/{id}/level',
  changePhoneVerifyStatus = '/manager/users/{id}/phone_verify_status',
  updateUser = '/manager/users/{id}',
  delete = '/manager/users/{id}',
  updateFinancialStatus = '/manager/users/{id}/financial-status',
}
export function exportUsers(params: BasicPageParams) {
  return defHttp.get({ url: Api.download, responseType: 'blob', params })
}

export function getUsers(params: BasicPageParams) {
  return defHttp.get<BasicListFetchResult<UserModel>>(
    { url: Api.users, params },
    { errorMessageMode: 'none' },
  )
}

export function getUniversities() {
  return defHttp.get<University[]>({ url: Api.universities })
}

export function getUser(id: number) {
  return defHttp.get<UserModel>({ url: Api.user.replace('{id}', id.toString()) })
}

export function setStatus({
  id,
  verificationStatus,
  rejectionReason,
  status,
}: {
  id: number
  verificationStatus: VerificationStatus
  rejectionReason?: RejectionReason
  status?: UserStatus
}) {
  return defHttp.post<OperationResponse>({
    url: Api.setStatus.replace('{id}', id.toString()),
    data: { verificationStatus, status, rejectionReason },
  })
}

export function operationBalance(
  operation: MeeCoinOperation,
  customer: number,
  amount: number,
  note: string,
) {
  const url = MeeCoinOperation.add == operation ? Api.addMeeCoin : Api.deductMeeCoin
  return defHttp.post<MeeCoinOperationResponse>({
    url,
    data: { customer, amount, note },
  })
}

export function distributeBalance(
  audienceType: string,
  audiences: number[],
  amount: number,
  note: string,
) {
  return defHttp.post<MeeCoinOperationResponse>({
    url: Api.distributeMeeCoin,
    data: { audienceType, audiences, amount, note },
  })
}

export function changeLevel(id: number, level: number) {
  return defHttp.post<any>({
    url: Api.changeLevel.replace('{id}', id.toString()),
    data: { level: level },
  })
}

export function changePhoneVerifyStatus(id: number, status: string) {
  return defHttp.post<any>({
    url: Api.changePhoneVerifyStatus.replace('{id}', id.toString()),
    data: { phoneVerificationStatus: status },
  })
}

/**
 * Update user information
 * @param id User ID
 * @param data User data to update
 * @returns Operation response
 */
export function updateUser(id: number, data: Partial<UserModel>) {
  return defHttp.put<OperationResponse>({
    url: Api.updateUser.replace('{id}', id.toString()),
    data,
  })
}

/**
 * Delete user
 * @param id User ID
 * @returns Operation response
 */
export function deleteUser(id: number) {
  return defHttp.delete<OperationResponse>({
    url: Api.delete.replace('{id}', id.toString()),
  })
}

/**
 * Update user financial status
 * @param id User ID
 * @param financialStatus Financial status to update
 * @returns Operation response
 */
export function updateUserFinancialStatus(id: number, financialStatus: string) {
  return defHttp.put<FinancialStatusUpdateResponse>({
    url: Api.updateFinancialStatus.replace('{id}', id.toString()),
    data: { financialStatus },
  })
}
