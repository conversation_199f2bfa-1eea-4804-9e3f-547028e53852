import { Gender, SourceFile } from '../../model/baseModel'

export interface UserModel {
  id: number
  username: string
  provider: string
  confirmed: boolean
  blocked: boolean
  createdAt: string
  fullname: string
  deleted: boolean
  deletedAt: null | Date
  phone: string
  birthday: string
  gender: Gender
  introduction: string
  location: string
  latitude: number
  longitude: number
  verificationEmail: string
  verificationType: VerificationType
  emailSentAt: null | string
  verificationStatus: VerificationStatus
  rejectionReason: RejectionReason
  balance: number
  avgRating: number
  countryCode: string
  regRefCode: string
  referralCode: string
  status: null | UserStatus
  avatar: SourceFile
  university: University
  photos?: SourceFile[]
  interests?: Interest[]
  fakename?: string
  visibility: boolean
  email: string
  level: number
  regionCode: string
  nationalityCode: string
  financialStatus: FinancialStatus
  phoneVerificationStatus: string
  title: string
  asset_verifications?: AssetVerification[]
  spouse?: SourceFile[]
  corporate?: SourceFile[]
  idcard?: SourceFile
}

export interface University {
  id: number
  name: string
}

export enum VerificationStatus {
  incomplete = 'incomplete',
  pending = 'pending',
  notVerifiedYet = 'not_verified_yet',
  verified = 'verified',
  rejected = 'rejected',
}

export enum VerificationType {
  email = 'email',
  certificate = 'certificate',
}

export interface SearchParams {
  page: number
  pageSize: number
  username?: string
  university?: number | null
  verificationType?: string
  verificationStatus?: string
  startDate?: string
  endDate?: string
  level?: number | null
  gender?: string
  latestAccessTime?: string
}

export enum UserStatus {
  graduate = 'GRADUATE',
  undergraduate = 'UNDERGRADUATE',
}

export interface Interest {
  id: number
  tag: string
  name: string
}

export enum MeeCoinOperation {
  add = 'ADD',
  deduct = 'DEDUCT',
}

export interface MeeCoinOperationResponse {
  id: number
  name: string
  type: string
  amount: number
  balance: number
  createdAt: string
  updatedAt: string
  targetName: any
  related: any
  ex: Ex
}

export interface Ex {
  note: string
}

export interface RejectionReason {
  en: string
  vi: string
}

export interface LightUserModel {
  id: number
  username?: string
}

export interface AssetVerification {
  id: number
  proof: SourceFile[]
  type: AssetVerificationType
  name: string
  description: string
}

export enum AssetVerificationType {
  RealEstate = 'RealEstate',
  FinancialAssets = 'FinancialAssets',
  CryptoCurrency = 'CryptoCurrency',
  Others = 'Others',
}

export enum AssetOwnerType {
  self = 'self',
  spousal = 'spousal',
  corporate = 'corporate',
}

export enum FinancialStatus {
  doNotAuthorize = 'doNotAuthorize',
  submitted = 'submitted',
  risingStar = 'risingStar',
  oneStar = 'oneStar',
  twoStar = 'twoStar',
  threeStar = 'threeStar',
  fourStar = 'fourStar',
  fiveStar = 'fiveStar',
  sixStar = 'sixStar',
  sevenStar = 'sevenStar',
}
