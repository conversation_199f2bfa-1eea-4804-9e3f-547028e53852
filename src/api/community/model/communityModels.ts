import { SourceFile } from '../../model/baseModel'

export interface PostListParams {
  authorName?: string
  boardId?: number
  blocked?: number
  endDate?: string
  page?: number
  pageSize?: number
  pinned?: number
  startDate?: string
}

export interface BoardModel {
  id: number
  name: string
  description?: string
  related?: string
  type?: string
}

export interface PostModel {
  authorUser: AuthorUser
  blocked: boolean
  content: string
  createdAt: string
  data: PostEXData
  id: number
  likeCount: number | null
  media: SourceFile[]
  pinned: boolean
  publishedAt: string
  removed: boolean
  title: null | string
  updatedAt: string
}

export interface PostEXData {
  commentCount: number
  dislikeCount: number
  likeCount: number
}

export interface PostDetailModel {
  authorUser: AuthorUser
  blocked: boolean
  board: Board
  content: string
  createdAt: string
  data: PostEXData
  id: number
  likeCount: null
  media: SourceFile[]
  pinned: boolean
  publishedAt: string
  removed: boolean
  title: null
  updatedAt: string
}

export interface AuthorUser {
  fakeName: string
  id: number
  username?: string
}

export interface Board {
  blocked: boolean
  createdAt: string
  description: string
  id: number
  locales: Locales
  name: string
  postingPermission: number
  related: null
  type: string
  updatedAt: string
}

export interface Locales {
  en: string
  vi: string
}

export interface CommentListParams {
  authorName?: string
  blocked?: number
  endDate?: string
  page?: number
  pageSize?: number
  postId?: number
  startDate?: string
}

export interface CommentModel {
  authorUser: AuthorUser
  blocked: boolean
  content: string
  createdAt: string
  data: CommentEXData
  id: number
  post: {
    id: number
  }
  removed: boolean
  updatedAt: string
}

export interface CommentEXData {
  dislikeCount: number
  likeCount: number
}
