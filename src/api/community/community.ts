import { defHttp } from '/@/utils/http/axios'
import { BasicListFetchResult, OperationResponse } from '../model/baseModel'
import {
  CommentListParams,
  CommentModel,
  PostDetailModel,
  PostListParams,
  PostModel,
  BoardModel,
} from './model/communityModels'

enum Api {
  activeBoards = '/community/active-boards',
  posts = '/community/posts',
  post = '/community/posts/{id}',
  comments = '/community/comments',
  comment = '/community/comments/{id}',
}

export function getActiveBoards() {
  return defHttp.get<BoardModel[]>({ url: Api.activeBoards })
}

export function getPosts(params: PostListParams) {
  return defHttp.get<BasicListFetchResult<PostModel>>({ url: Api.posts, params })
}

export function getPost(id: number) {
  return defHttp.get<PostDetailModel>({ url: Api.post.replace('{id}', id.toString()) })
}

export function updatePostStatus(id: number, data: { pinned?: boolean; blocked?: boolean }) {
  return defHttp.put<OperationResponse>({ url: Api.post.replace('{id}', id.toString()), data })
}

export function updatePost(id: number, data) {
  return defHttp.put<OperationResponse>({ url: Api.post.replace('{id}', id.toString()), data })
}

export function createPost(data) {
  return defHttp.post<OperationResponse>({ url: Api.posts, data })
}

export function getComments(params: CommentListParams) {
  return defHttp.get<BasicListFetchResult<CommentModel>>({ url: Api.comments, params })
}

export function updateCommentStatus(id: number, data: { blocked: boolean }) {
  return defHttp.put<OperationResponse>({ url: Api.comment.replace('{id}', id.toString()), data })
}
