export interface ReportSearchParams {
  page: number
  pageSize: number
  action?: string
  processed?: number | null
  startDate?: string
  endDate?: string
}

export interface Report {
  id: number
  reasons: Reason[]
  action: ReportAction | null
  tempBanDuration: any
  processedAt: ReportProcessed | null
  note?: string | null
  createdAt: string
  updatedAt: string
  reporter: Reporter
  reported: Reported
  attachments: Attachment[]
}

export interface Reason {
  type: string
  text: string
}

export interface Reported {
  id: number
  username: string
  blocked: boolean
  deleted: boolean
}

export interface Reporter {
  id: number
  username: string
  blocked: boolean
  deleted: boolean
}

export interface Attachment {
  id: number
  url: string
}

export enum ReportAction {
  permanentBan = 'PERMA_BAN',
  tempBan = 'TEMP_BAN',
  warning = 'WARNING',
  nothing = 'NOTHING',
}

export enum ReportProcessed {
  unhandled,
  handled,
}
