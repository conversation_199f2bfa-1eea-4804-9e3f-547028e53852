import { BasicListFetchResult, OperationResponse } from '../model/baseModel'
import { ReportSearchParams, Report, ReportAction } from './model/reportModel'
import { defHttp } from '/@/utils/http/axios'
enum Api {
  reports = '/manager/reports',
  handleReport = '/manager/reports/{id}/process',
}

export function getReports(params: ReportSearchParams) {
  return defHttp.get<BasicListFetchResult<Report>>(
    { url: Api.reports, params },
    { errorMessageMode: 'none' },
  )
}

export function handleReport(
  id: number,
  action: ReportAction,
  note: string,
  tempBanDuration?: number,
) {
  return defHttp.post<OperationResponse>(
    {
      url: Api.handleReport.replace('{id}', id.toString()),
      data: {
        action,
        note,
        tempBanDuration,
      },
    },
    { errorMessageMode: 'none' },
  )
}
