import { BasicListFetchResult } from '../model/baseModel'
import { FeedbackSearchParams, FeedbackModel } from './model/feedbackModel'
import { defHttp } from '/@/utils/http/axios'
enum Api {
  feedbacks = '/manager/feedbacks',
}

export function getFeedbacks(params: FeedbackSearchParams) {
  return defHttp.get<BasicListFetchResult<FeedbackModel>>(
    { url: Api.feedbacks, params },
    { errorMessageMode: 'none' },
  )
}
