import { defHttp } from '/@/utils/http/axios'
import { preRegUsersModel } from './model/preRegModel'
import { BasicPageParams } from '../model/baseModel'

enum Api {
  preRegUsers = '/content-manager/collection-types/api::pre-membership.pre-membership',
}

export function getPreRegUsers(params: BasicPageParams) {
  return defHttp.get<preRegUsersModel>(
    { url: Api.preRegUsers, params },
    { errorMessageMode: 'none' },
  )
}
