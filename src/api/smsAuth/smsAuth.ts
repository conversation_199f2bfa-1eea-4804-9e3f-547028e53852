import { BasicListFetchResult } from '../model/baseModel'
import { SmsAuthSearchParams, SmsAuthModel } from './model/smsAuthModel'
import { defHttp } from '/@/utils/http/axios'
enum Api {
  smsAuthLogs = '/manager/sms',
}

export function getSmsAuthLogs(params: SmsAuthSearchParams) {
  return defHttp.get<BasicListFetchResult<SmsAuthModel>>(
    { url: Api.smsAuthLogs, params },
    { errorMessageMode: 'none' },
  )
}
