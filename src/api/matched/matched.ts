import { BasicListFetchResult } from '../model/baseModel'
import { MatchedSearchParams, MatchedModel } from './model/matchedModel'
import { defHttp } from '/@/utils/http/axios'
enum Api {
  matches = '/manager/matches',
}

export function getMatchedUsers(params: MatchedSearchParams) {
  return defHttp.get<BasicListFetchResult<MatchedModel>>(
    { url: Api.matches, params },
    { errorMessageMode: 'none' },
  )
}
