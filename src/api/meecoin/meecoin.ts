import { BasicListFetchResult } from '../model/baseModel'
import { MeecoinSearchParams, Transaction } from './model/meecoinModel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  transactions = '/manager/transactions',
}

export function getTransactions(params: MeecoinSearchParams) {
  return defHttp.get<BasicListFetchResult<Transaction>>(
    { url: Api.transactions, params },
    { errorMessageMode: 'none' },
  )
}
