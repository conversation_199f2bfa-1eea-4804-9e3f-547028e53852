export interface MeecoinSearchParams {
  page: number
  pageSize: number
  username?: string
  type?: TransactionType
  startDate?: string
  endDate?: string
}

export interface Transaction {
  id: number
  name: string
  type: string
  amount: number
  balance: number
  createdAt: Date
  updatedAt: Date
  targetName: string | null
  related: string | null
  ex: Ex
  customer: Customer
}

export interface Ex {
  note: string
}

export interface Customer {
  id: number
  username: string
}

export enum TransactionType {
  RECHARGE = 'RECHAR<PERSON>',
  UNLOCK_EVAL = 'UNLOCK_EVAL',
  UNLOCK_HEART = 'UNLOCK_HEART',
  UNLOCK_PAST_REC = 'UNLOCK_PAST_REC',
  SENT_HEART = 'SENT_HEART',
  SENT_DOUBLE_HEART = 'SENT_DOUBLE_HEART',
  PREMIUM_REC = 'PREMIUM_REC',
  DOUBLE_HEART_REFUND = 'DOUBLE_HEART_REFUND',
  EVAL_BONUS = 'EVAL_BONUS',
  PRE_REG_BONUS = 'PRE_REG_BONUS',
  REF_1_BONUS = 'REF_1_BONUS',
  REF_2_BONUS = 'REF_2_BONUS',
  DEPOSIT = 'DEPOSIT',
}
