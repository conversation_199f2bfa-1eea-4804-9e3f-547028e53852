import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/messages',
  name: 'messages',
  component: LAYOUT,
  redirect: '/messages/notifications',
  meta: {
    orderNo: 100,
    hideChildrenInMenu: true,
    icon: 'mdi:message',
    title: t('routes.messages.page'),
  },
  children: [
    {
      path: 'notifications',
      name: 'notifications',
      component: () => import('/@/views/messages/index.vue'),
      meta: {
        title: 'Sent Messages',
        icon: 'mdi:list-box',
        hideMenu: true,
      },
    },
  ],
}

export default setup
