import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/dating',
  name: 'dating',
  component: LAYOUT,
  redirect: '/dating/daily',
  meta: {
    orderNo: 20,
    icon: 'mdi:heart',
    title: t('routes.dating.main'),
  },
  children: [
    {
      path: 'setting',
      name: 'dailySetting',
      component: () => import('/@/views/daily/setting.vue'),
      meta: {
        title: t('routes.dating.setting'),
        icon: 'ri:settings-5-fill',
      },
    },
    {
      path: 'daily',
      name: 'dailyList',
      component: () => import('/@/views/daily/index.vue'),
      meta: {
        title: t('routes.dating.dailycard'),
        icon: 'ri:user-received-fill',
      },
    },
    {
      path: 'heart',
      name: 'likeHeartList',
      component: () => import('/@/views/likeHeart/index.vue'),
      meta: {
        title: t('routes.dating.heartsent'),
        icon: 'ri:hand-heart-fill',
      },
    },
    {
      path: 'matched',
      name: 'matchedList',
      component: () => import('/@/views/matched/index.vue'),
      meta: {
        title: t('routes.dating.matched'),
        icon: 'ri:hearts-fill',
      },
    },
  ],
}

export default setup
