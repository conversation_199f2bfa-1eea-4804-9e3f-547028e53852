import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/meecoin',
  name: 'meecoin',
  component: LAYOUT,
  redirect: '/meecoin/index',
  meta: {
    orderNo: 19,
    icon: 'akar-icons:coin',
    title: t('main.meecoinPage'),
    hideChildrenInMenu: true,
  },
  children: [
    {
      path: 'index',
      name: 'meecoinList',
      component: () => import('/@/views/meecoin/index.vue'),
      meta: {
        title: 'history',
        icon: 'akar-icons:coin',
        hideMenu: true,
      },
    },
  ],
}

export default setup
