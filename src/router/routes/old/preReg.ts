import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/preReg',
  name: 'preRegPage',
  component: LAYOUT,
  redirect: '/preReg/index',
  meta: {
    orderNo: 90000,
    hideChildrenInMenu: true,
    icon: 'mdi:register',
    title: t('routes.preReg.page'),
  },
  children: [
    {
      path: 'index',
      name: 'preRegPageList',
      component: () => import('/@/views/preReg/index.vue'),
      meta: {
        title: t('routes.preReg.page'),
        icon: 'mdi:register',
        hideMenu: true,
      },
    },
  ],
}

export default setup
