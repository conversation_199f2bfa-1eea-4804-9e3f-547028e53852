import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/smsAuth',
  name: 'smsAuth',
  component: LAYOUT,
  redirect: '/smsAuth/index',
  meta: {
    orderNo: 130,
    hideChildrenInMenu: true,
    icon: 'ri:message-3-line',
    title: 'SMS Auth',
  },
  children: [
    {
      path: 'index',
      name: 'smsAuthList',
      component: () => import('/@/views/smsAuth/index.vue'),
      meta: {
        title: t('main.feedbackPage'),
        icon: 'ri:message-3-line',
        hideMenu: true,
      },
    },
  ],
}

export default setup
