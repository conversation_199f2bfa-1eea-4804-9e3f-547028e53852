import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/feedback',
  name: 'feedback',
  component: LAYOUT,
  redirect: '/feedback/index',
  meta: {
    orderNo: 101,
    hideChildrenInMenu: true,
    icon: 'ri:feedback-fill',
    title: t('main.feedbackPage'),
  },
  children: [
    {
      path: 'index',
      name: 'feedbackList',
      component: () => import('/@/views/feedback/index.vue'),
      meta: {
        title: t('main.feedbackPage'),
        icon: 'ri:feedback-fill',
        hideMenu: true,
      },
    },
  ],
}

export default setup
