import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/users',
  name: 'users',
  component: LAYOUT,
  redirect: '/users/index',
  meta: {
    orderNo: 10,
    hideChildrenInMenu: true,
    icon: 'mdi:users',
    title: t('routes.users.page'),
  },
  children: [
    {
      path: 'index',
      name: 'usersList',
      component: () => import('/@/views/users/index.vue'),
      meta: {
        title: t('routes.users.page'),
        icon: 'mdi:users',
        hideMenu: true,
      },
    },
    {
      path: ':id',
      name: 'user',
      component: () => import('/@/views/users/user.vue'),
      meta: {
        title: t('routes.users.detailPage'),
        hideMenu: true,
      },
    },
  ],
}

export default setup
