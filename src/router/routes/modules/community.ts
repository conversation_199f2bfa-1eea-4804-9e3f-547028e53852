import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/community',
  name: 'community',
  component: LAYOUT,
  redirect: '/community/posts',
  meta: {
    orderNo: 60,
    hideChildrenInMenu: false,
    icon: 'jam:messages-f',
    title: t('routes.community.page'),
  },
  children: [
    {
      path: '/community/posts',
      name: 'postsList',
      component: () => import('/@/views/community/posts/index.vue'),
      meta: {
        title: t('routes.community.postsListPage'),
        icon: 'material-symbols:article-rounded',
        hideMenu: false,
      },
    },
    {
      path: '/community/posts/:id',
      name: 'postDetail',
      component: () => import('/@/views/community/posts/detail.vue'),
      meta: {
        title: t('routes.community.postDetailPage'),
        icon: 'material-symbols:article-rounded',
        hideMenu: true,
      },
    },
    {
      path: '/community/comments',
      name: 'commentsList',
      component: () => import('/@/views/community/comments/index.vue'),
      meta: {
        title: t('routes.community.commentsListPage'),
        icon: 'ic:round-comment',
        hideMenu: false,
      },
    },
  ],
}

export default setup
