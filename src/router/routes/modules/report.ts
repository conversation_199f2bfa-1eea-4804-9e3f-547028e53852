import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/report',
  name: 'report',
  component: LAYOUT,
  redirect: '/report/index',
  meta: {
    orderNo: 110,
    hideChildrenInMenu: true,
    icon: 'solar:document-bold',
    title: t('main.reportPage'),
  },
  children: [
    {
      path: 'index',
      name: 'reportList',
      component: () => import('/@/views/report/index.vue'),
      meta: {
        title: t('main.reportPage'),
        icon: 'solar:document-bold',
        hideMenu: true,
      },
    },
  ],
}

export default setup
