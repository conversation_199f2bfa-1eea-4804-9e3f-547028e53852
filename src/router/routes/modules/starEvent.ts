import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/star-event',
  name: 'starEvent',
  component: LAYOUT,
  redirect: '/star-event/list',
  meta: {
    orderNo: 65,
    hideChildrenInMenu: false,
    icon: 'ant-design:star-outlined',
    title: t('routes.starEvent.page'),
  },
  children: [
    {
      path: '/star-event/list',
      name: 'starEventList',
      component: () => import('/@/views/star-event/list/index.vue'),
      meta: {
        title: t('routes.starEvent.listPage'),
        icon: 'ant-design:star-outlined',
        hideMenu: false,
      },
    },
    {
      path: '/star-event/weekly-check',
      name: 'starEventSchedule',
      component: () => import('/@/views/star-event/schedule/index.vue'),
      meta: {
        title: t('routes.starEvent.weeklyCheckPage'),
        icon: 'ant-design:calendar-outlined',
        hideMenu: false,
      },
    },
    {
      path: '/star-event/create',
      name: 'starEventCreate',
      component: () => import('/@/views/star-event/form/index.vue'),
      meta: {
        title: t('routes.starEvent.createPage'),
        icon: 'ant-design:star-outlined',
        hideMenu: true,
        currentActiveMenu: '/star-event/list',
      },
    },
    {
      path: '/star-event/edit/:id',
      name: 'starEventEdit',
      component: () => import('/@/views/star-event/form/index.vue'),
      meta: {
        title: t('routes.starEvent.editPage'),
        icon: 'ant-design:star-outlined',
        hideMenu: true,
        currentActiveMenu: '/star-event/list',
      },
    },
  ],
}

export default setup
