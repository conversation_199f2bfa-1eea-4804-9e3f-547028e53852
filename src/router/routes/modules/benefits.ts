import type { AppRouteModule } from '/@/router/types'

import { LAYOUT } from '/@/router/constant'
import { t } from '/@/hooks/web/useI18n'

const setup: AppRouteModule = {
  path: '/benefits',
  name: 'benefits',
  component: LAYOUT,
  redirect: '/benefits/boards',
  meta: {
    orderNo: 70,
    hideChildrenInMenu: false,
    icon: 'ant-design:gift-outlined',
    title: t('routes.benefits.page'),
  },
  children: [
    // Benefits Boards
    {
      path: '/benefits/boards',
      name: 'benefitsBoards',
      component: () => import('/@/views/benefits/boards/index.vue'),
      meta: {
        title: t('routes.benefits.boards.listPage'),
        icon: 'ant-design:appstore-outlined',
        hideMenu: false,
      },
    },
    {
      path: '/benefits/boards/create',
      name: 'benefitsBoardCreate',
      component: () => import('/@/views/benefits/boards/form/index.vue'),
      meta: {
        title: t('routes.benefits.boards.createPage'),
        icon: 'ant-design:plus-outlined',
        hideMenu: true,
        currentActiveMenu: '/benefits/boards',
      },
    },
    {
      path: '/benefits/boards/edit/:id',
      name: 'benefitsBoardEdit',
      component: () => import('/@/views/benefits/boards/form/index.vue'),
      meta: {
        title: t('routes.benefits.boards.editPage'),
        icon: 'ant-design:edit-outlined',
        hideMenu: true,
        currentActiveMenu: '/benefits/boards',
      },
    },
    // Benefits Coupons
    {
      path: '/benefits/coupons',
      name: 'benefitsCoupons',
      component: () => import('/@/views/benefits/coupons/index.vue'),
      meta: {
        title: t('routes.benefits.coupons.listPage'),
        icon: 'ant-design:tags-outlined',
        hideMenu: false,
      },
    },
    {
      path: '/benefits/coupons/create',
      name: 'benefitsCouponCreate',
      component: () => import('/@/views/benefits/coupons/form/index.vue'),
      meta: {
        title: t('routes.benefits.coupons.createPage'),
        icon: 'ant-design:plus-outlined',
        hideMenu: true,
        currentActiveMenu: '/benefits/coupons',
      },
    },
    {
      path: '/benefits/coupons/edit/:id',
      name: 'benefitsCouponEdit',
      component: () => import('/@/views/benefits/coupons/form/index.vue'),
      meta: {
        title: t('routes.benefits.coupons.editPage'),
        icon: 'ant-design:edit-outlined',
        hideMenu: true,
        currentActiveMenu: '/benefits/coupons',
      },
    },
  ],
}

export default setup
