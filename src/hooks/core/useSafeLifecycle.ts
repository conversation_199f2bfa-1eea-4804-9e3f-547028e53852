import { getCurrentInstance, onMounted, onUnmounted, onBeforeUnmount } from 'vue'
import { tryOnMounted, tryOnUnmounted, tryOnBeforeUnmount } from '@vueuse/core'

/**
 * 安全的生命周期钩子，防止在组件卸载过程中出现警告
 */
export function useSafeLifecycle() {
  const instance = getCurrentInstance()

  /**
   * 安全的 onMounted 钩子
   */
  const safeOnMounted = (fn: () => void) => {
    if (instance) {
      onMounted(fn)
    } else {
      tryOnMounted(fn)
    }
  }

  /**
   * 安全的 onUnmounted 钩子
   */
  const safeOnUnmounted = (fn: () => void) => {
    if (instance) {
      tryOnUnmounted(fn)
    } else {
      // 如果没有组件实例，直接执行清理函数
      fn()
    }
  }

  /**
   * 安全的 onBeforeUnmount 钩子
   */
  const safeOnBeforeUnmount = (fn: () => void) => {
    if (instance) {
      tryOnBeforeUnmount(fn)
    } else {
      // 如果没有组件实例，直接执行清理函数
      fn()
    }
  }

  return {
    safeOnMounted,
    safeOnUnmounted,
    safeOnBeforeUnmount,
  }
}

/**
 * 检查当前是否有活跃的组件实例
 */
export function hasActiveInstance(): boolean {
  return !!getCurrentInstance()
}

/**
 * 安全地执行需要组件实例的操作
 */
export function withSafeInstance<T>(fn: () => T, fallback?: () => T): T | undefined {
  if (hasActiveInstance()) {
    return fn()
  } else if (fallback) {
    return fallback()
  }
  return undefined
}
