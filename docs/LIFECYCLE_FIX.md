# Vue 生命周期警告修复

## 问题描述

在使用 Vue 3 + Ant Design Vue 的项目中，出现了以下警告：

```
[Vue warn]: onUnmounted is called when there is no active component instance to be associated with. 
Lifecycle injection APIs can only be used during execution of setup(). 
If you are using async setup(), make sure to register lifecycle hooks before the first await statement.
```

## 问题原因

这个警告通常出现在以下情况：

1. **异步组件卸载**：当组件在异步操作过程中被卸载时，生命周期钩子可能在没有活跃组件实例的情况下被调用
2. **KeepAlive 缓存问题**：使用 KeepAlive 时，组件的生命周期变得复杂
3. **条件性注册生命周期钩子**：在某些条件下注册生命周期钩子可能导致时序问题
4. **第三方库的生命周期管理**：某些第三方库可能没有正确处理组件卸载

## 解决方案

### 1. 使用 @vueuse/core 的安全生命周期钩子

将所有的 `onUnmounted`、`onBeforeUnmount` 替换为 `tryOnUnmounted`、`tryOnBeforeUnmount`：

```typescript
// 修改前
import { onUnmounted } from 'vue'
onUnmounted(() => {
  // 清理逻辑
})

// 修改后
import { tryOnUnmounted } from '@vueuse/core'
tryOnUnmounted(() => {
  // 清理逻辑
})
```

### 2. 修复的文件列表

以下文件已经被修复：

- `src/components/Modal/src/hooks/useModal.ts`
- `src/hooks/core/useAttrs.ts`
- `src/hooks/event/useScroll.ts`
- `src/hooks/web/useWatermark.ts`
- `src/components/Modal/src/components/ModalWrapper.vue`
- `src/directives/clickOutside.ts`

### 3. 新增的工具函数

创建了 `src/hooks/core/useSafeLifecycle.ts` 提供安全的生命周期钩子：

```typescript
import { useSafeLifecycle } from '/@/hooks/core/useSafeLifecycle'

const { safeOnMounted, safeOnUnmounted, safeOnBeforeUnmount } = useSafeLifecycle()

safeOnUnmounted(() => {
  // 安全的清理逻辑
})
```

### 4. 最佳实践

1. **总是使用 tryOn* 系列函数**：对于可能在异步环境中调用的生命周期钩子
2. **检查组件实例**：在注册生命周期钩子前检查是否有活跃的组件实例
3. **错误处理**：在清理函数中添加 try-catch 来处理可能的错误
4. **及时清理**：确保所有的事件监听器、定时器等都被正确清理

### 5. 验证修复

修复后，以下警告应该不再出现：
- `onUnmounted is called when there is no active component instance`
- 相关的生命周期警告

## 注意事项

1. 这些修改主要是预防性的，确保在各种边缘情况下都能正常工作
2. 使用 `tryOn*` 函数不会影响正常的组件生命周期
3. 如果仍然出现警告，可能需要检查第三方库的使用方式
