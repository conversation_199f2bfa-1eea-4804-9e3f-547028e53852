# About Directory

This directory contains standalone HTML pages that can be accessed directly without using the Vue.js application layout.

## Files

- `index.html` - Main about page with navigation to other pages
- `privacy.html` - Privacy policy page
- `README.md` - This documentation file

## Access URLs

When the application is running, these pages can be accessed at:

### Vue Router (Recommended)
- About page: `http://localhost:3000/about`
- Privacy policy: `http://localhost:3000/about/privacy`

### Static Files (Alternative)
- About page: `http://localhost:3000/about/index.html`
- Privacy policy: `http://localhost:3000/about/privacy.html`

## Features

- **Dual Access Methods**: 
  - Vue Router integration with `ignoreAuth: true` (no login required)
  - Static HTML files for direct access
- **Responsive design**: All pages are mobile-friendly
- **Modern styling**: Clean, professional appearance with hover effects
- **Navigation**: Easy navigation between pages and back to the main application
- **No Authentication Required**: Pages can be accessed without logging in

## Customization

You can customize these pages by:

1. Editing the HTML content directly
2. Modifying the inline CSS styles
3. Adding new pages to this directory
4. Updating contact information in the privacy policy

## Notes

- **Vue Router Integration**: The recommended way is to use `/about` and `/about/privacy` routes
- **Static File Fallback**: HTML files in `public/about/` serve as backup/alternative access
- **No Authentication**: Both access methods bypass login requirements using `ignoreAuth: true`
- **Responsive Design**: All pages work well on mobile devices
- **Consistent Styling**: Vue components and static HTML maintain similar appearance 