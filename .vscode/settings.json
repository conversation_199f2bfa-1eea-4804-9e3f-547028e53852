{"typescript.tsdk": "./node_modules/typescript/lib", "volar.tsPlugin": true, "volar.tsPluginStatus": false, "npm.packageManager": "pnpm", "editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode", "files.eol": "\n", "search.exclude": {"**/node_modules": true, "**/*.log": true, "**/*.log*": true, "**/bower_components": true, "**/dist": true, "**/elehukouben": true, "**/.git": true, "**/.gitignore": true, "**/.svn": true, "**/.DS_Store": true, "**/.idea": true, "**/.vscode": false, "**/yarn.lock": true, "**/tmp": true, "out": true, "dist": true, "node_modules": true, "CHANGELOG.md": true, "examples": true, "res": true, "screenshots": true, "yarn-error.log": true, "**/.yarn": true}, "files.exclude": {"**/.cache": true, "**/.editorconfig": true, "**/.eslintcache": true, "**/bower_components": true, "**/.idea": true, "**/tmp": true, "**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/.vscode/**": true, "**/node_modules/**": true, "**/tmp/**": true, "**/bower_components/**": true, "**/dist/**": true, "**/yarn.lock": true}, "stylelint.enable": true, "stylelint.validate": ["css", "less", "postcss", "scss", "vue", "sass"], "path-intellisense.mappings": {"/@/": "${workspaceRoot}/src"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[vue]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}}, "i18n-ally.localesPaths": ["src/locales/lang"], "i18n-ally.keystyle": "nested", "i18n-ally.sortKeys": true, "i18n-ally.namespace": true, "i18n-ally.pathMatcher": "{locale}/{namespaces}.{ext}", "i18n-ally.enabledParsers": ["ts"], "i18n-ally.sourceLanguage": "en", "i18n-ally.displayLanguage": "zh-CN", "i18n-ally.enabledFrameworks": ["vue", "react"], "cSpell.words": ["vben", "windi", "browserslist", "tailwindcss", "esnext", "antv", "<PERSON><PERSON><PERSON>", "sider", "pinia", "sider", "nprogress", "INTLIFY", "stylelint", "esno", "vite<PERSON><PERSON>", "sortablejs", "mockjs", "codemirror", "iconify", "commitlint", "vditor", "echarts", "cropperjs", "logicflow", "vueuse", "zxcvbn", "lintstagedrc", "brotli", "tailwindcss", "sider", "pnpm", "antd"], "vetur.format.scriptInitialIndent": true, "vetur.format.styleInitialIndent": true, "vetur.validation.script": false}