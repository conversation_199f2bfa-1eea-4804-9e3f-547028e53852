# Benefits 模块实现文档

## 概述

本文档描述了为 Starcheck Admin Frontend 项目实现的 Benefits 模块功能。该模块包含两个主要子模块：BenefitsBoard（福利板块）和 BenefitsCoupon（福利券）。

## 实现的功能

### 1. 路由配置
- **文件位置**: `src/router/routes/modules/benefits.ts`
- **功能**: 定义了 Benefits 模块的所有路由，包括列表页、创建页、编辑页
- **路由结构**:
  - `/benefits/boards` - 福利板块列表
  - `/benefits/boards/create` - 创建福利板块
  - `/benefits/boards/edit/:id` - 编辑福利板块
  - `/benefits/coupons` - 福利券列表
  - `/benefits/coupons/create` - 创建福利券
  - `/benefits/coupons/edit/:id` - 编辑福利券

### 2. API 接口
- **文件位置**: `src/api/benefits/benefits.ts`
- **数据模型**: `src/api/benefits/model/benefitsModels.ts`
- **功能**: 实现了完整的 CRUD 操作
  - BenefitsBoard: 获取列表、获取详情、创建、更新、删除
  - BenefitsCoupon: 获取列表、获取详情、创建、更新、删除

### 3. 国际化配置
- **支持语言**: 英文、韩文、中文
- **文件位置**:
  - 英文: `src/locales/lang/en/benefits.ts`, `src/locales/lang/en/routes/benefits.ts`
  - 韩文: `src/locales/lang/ko/benefits.ts`, `src/locales/lang/ko/routes/benefits.ts`
  - 中文: `src/locales/lang/zh-CN/benefits.ts`, `src/locales/lang/zh-CN/routes/benefits.ts`
- **功能**: 完整的多语言支持，包括页面标题、表单标签、按钮文本、验证消息等

### 4. BenefitsBoard 功能

#### 列表页面 (`src/views/benefits/boards/index.vue`)
- **功能特性**:
  - 搜索功能（按名称、类型、状态）
  - 分页显示
  - 表格展示（名称、描述、类型、状态、排序、福利券数量、创建时间）
  - 操作按钮（编辑、删除）
  - 状态标签显示

#### 表单页面 (`src/views/benefits/boards/form/index.vue`)
- **功能特性**:
  - 创建和编辑模式
  - 表单验证
  - 多语言名称设置（英文、韩文）
  - 类型选择（公开/私有）
  - 开关控制（屏蔽状态、默认板块）
  - 数字输入（排序、发帖权限）

### 5. BenefitsCoupon 功能

#### 列表页面 (`src/views/benefits/coupons/index.vue`)
- **功能特性**:
  - 高级搜索（标题、板块、等级、置顶状态、屏蔽状态）
  - 分页显示
  - 表格展示（标题、内容预览、媒体预览、板块、等级、状态、点赞数、过期时间、创建时间）
  - 状态智能判断（活跃、已过期、已屏蔽、已删除）
  - 操作按钮（编辑、删除）

#### 表单页面 (`src/views/benefits/coupons/form/index.vue`)
- **功能特性**:
  - 双栏布局（左侧表单，右侧富文本编辑器）
  - Vditor 富文本编辑器集成
  - 媒体文件上传（图片、视频、音频）
  - 板块选择（动态加载）
  - 日期时间选择器（过期时间）
  - 开关控制（置顶、屏蔽、删除状态）
  - 响应式设计

## 技术特性

### 1. 组件设计
- 遵循项目现有的组件规范
- 使用 Ant Design Vue 组件库
- 响应式设计，支持移动端

### 2. 数据处理
- TypeScript 类型安全
- 表单验证
- 错误处理
- 加载状态管理

### 3. 用户体验
- 搜索和过滤功能
- 分页处理
- 操作确认对话框
- 成功/错误消息提示
- 加载状态指示

### 4. 富文本编辑
- Vditor 编辑器集成
- 图片上传支持
- 所见即所得编辑
- 工具栏自定义

### 5. 文件上传
- 多文件上传支持
- 文件类型限制
- 上传进度显示
- 预览功能

## 数据模型

### BenefitsBoard
```typescript
interface BenefitsBoardModel {
  id: number
  name: string
  locales?: { en?: string; ko?: string }
  description?: string
  related?: string
  blocked: boolean
  type: 'public' | 'private'
  creator?: User
  postingPermission?: number
  subscribers?: User[]
  coupons?: BenefitsCouponModel[]
  order?: number
  isDefault: boolean
  userLevelLimited?: string
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
}
```

### BenefitsCoupon
```typescript
interface BenefitsCouponModel {
  id: number
  title: string
  content?: string
  media?: SourceFile[]
  pinned: boolean
  blocked: boolean
  removed: boolean
  likeCount: number
  board?: { id: number; name: string; type: string }
  likers?: User[]
  expiresAt?: string
  phone?: string
  level?: number
  description?: string
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
}
```

## 使用说明

### 访问模块
1. 登录管理后台
2. 在左侧菜单中找到"福利"模块
3. 选择"福利板块"或"福利券"子模块

### 管理福利板块
1. 查看板块列表，使用搜索功能筛选
2. 点击"创建"按钮添加新板块
3. 点击编辑按钮修改现有板块
4. 使用删除功能移除不需要的板块

### 管理福利券
1. 查看福利券列表，使用高级搜索功能
2. 点击"创建"按钮添加新福利券
3. 使用富文本编辑器编写福利券内容
4. 上传相关媒体文件
5. 设置过期时间和其他属性
6. 保存并发布福利券

## 注意事项

1. **权限控制**: 确保用户具有相应的权限才能访问和操作
2. **数据验证**: 所有表单都包含客户端验证，但服务端验证同样重要
3. **文件上传**: 注意文件大小限制和格式要求
4. **国际化**: 支持多语言切换，确保内容的本地化
5. **响应式**: 界面适配不同屏幕尺寸

## 扩展建议

1. **批量操作**: 可以添加批量删除、批量状态更改功能
2. **导出功能**: 添加数据导出为 Excel 或 CSV 格式
3. **统计分析**: 添加福利券使用统计和分析功能
4. **通知系统**: 集成消息通知，提醒用户福利券即将过期
5. **审核流程**: 添加福利券发布前的审核机制
